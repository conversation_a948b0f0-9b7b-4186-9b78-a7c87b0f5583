# 🚀 Deploy VWork lên <PERSON>der - Hướng dẫn nhanh

## ✅ Kiểm tra trước khi deploy

Chạy script kiểm tra:
```bash
node deploy-render.js
```

## 📋 <PERSON><PERSON><PERSON> bước deploy

### 1. Push code lên GitHub
```bash
git add .
git commit -m "Prepare for Render deployment"
git push origin main
```

### 2. Deploy lên Render

1. **Đăng nhập Render Dashboard**
   - Truy cập: https://dashboard.render.com
   - Đăng nhập bằng GitHub account

2. **Tạo Blueprint**
   - Click "New" → "Blueprint"
   - Connect GitHub repository
   - Render sẽ tự động detect `render.yaml`

3. **Cấu hình Environment Variables**
   
   **API Gateway:**
   ```
   NODE_ENV=production
   PORT=3000
   AUTH_SERVICE_URL=https://vwork-auth-service.onrender.com
   USER_SERVICE_URL=https://vwork-user-service.onrender.com
   PROJECT_SERVICE_URL=https://vwork-project-service.onrender.com
   CHAT_SERVICE_URL=https://vwork-chat-service.onrender.com
   PAYMENT_SERVICE_URL=https://vwork-payment-service.onrender.com
   CORS_ORIGIN=https://vwork-frontend.onrender.com
   ```

   **Auth Service:**
   ```
   NODE_ENV=production
   PORT=3001
   JWT_SECRET=your-secure-jwt-secret-here
   FIREBASE_PROJECT_ID=vwork-786c3
   ```

   **User Service:**
   ```
   NODE_ENV=production
   PORT=3002
   FIREBASE_PROJECT_ID=vwork-786c3
   ```

   **Project Service:**
   ```
   NODE_ENV=production
   PORT=3003
   FIREBASE_PROJECT_ID=vwork-786c3
   ```

   **Chat Service:**
   ```
   NODE_ENV=production
   PORT=3004
   FIREBASE_PROJECT_ID=vwork-786c3
   ```

   **Payment Service:**
   ```
   NODE_ENV=production
   PORT=3005
   FIREBASE_PROJECT_ID=vwork-786c3
   ```

### 3. Test sau khi deploy

**Health Checks:**
- API Gateway: `https://vwork-api-gateway.onrender.com/health`
- Auth Service: `https://vwork-auth-service.onrender.com/health`
- User Service: `https://vwork-user-service.onrender.com/health`
- Project Service: `https://vwork-project-service.onrender.com/health`
- Chat Service: `https://vwork-chat-service.onrender.com/health`
- Payment Service: `https://vwork-payment-service.onrender.com/health`

**Frontend:**
- Main App: `https://vwork-frontend.onrender.com`

## 🔧 Troubleshooting

### Lỗi thường gặp:

1. **Build failed**
   - Kiểm tra dependencies trong package.json
   - Đảm bảo Node.js version tương thích

2. **Health check failed**
   - Kiểm tra endpoint `/health` hoạt động
   - Xem logs trong Render Dashboard

3. **CORS errors**
   - Cấu hình CORS_ORIGIN đúng
   - Kiểm tra CORS settings trong API Gateway

4. **Service không kết nối được**
   - Kiểm tra environment variables
   - Đảm bảo service URLs đúng

### Debug Commands:
```bash
# Xem logs trong Render Dashboard
# Hoặc sử dụng Render CLI
render logs --service vwork-api-gateway
```

## 📊 Monitoring

- **Logs**: Xem trong Render Dashboard
- **Health**: Monitor `/health` endpoints
- **Performance**: Sử dụng Render Analytics
- **Errors**: Check error logs thường xuyên

## 🔐 Security

- Sử dụng environment variables cho sensitive data
- Không commit API keys vào repository
- Backup configuration và database
- Monitor access logs

## 📞 Support

Nếu gặp vấn đề:
1. Kiểm tra logs trong Render Dashboard
2. Test health endpoints
3. Verify environment variables
4. Check service connectivity

---

**🎉 Chúc mừng! VWork đã được deploy thành công lên Render!** 