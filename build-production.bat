@echo off
echo 🚀 Building VWork for Production...

cd client

echo 🧹 Removing debug/test files...
if exist "src\pages\UserDebugPage.js" del "src\pages\UserDebugPage.js"
if exist "src\pages\TestUserStatus.js" del "src\pages\TestUserStatus.js"  
if exist "src\pages\LoginFlowTest.js" del "src\pages\LoginFlowTest.js"
if exist "src\pages\ProfileSetupTest.js" del "src\pages\ProfileSetupTest.js"
if exist "src\utils\firebaseDebug.js" del "src\utils\firebaseDebug.js"
if exist "src\utils\backendTest.js" del "src\utils\backendTest.js"
if exist "src\components\common\BackendDebug.js" del "src\components\common\BackendDebug.js"

echo 📦 Installing dependencies...
npm ci

echo 🔨 Building React application...
set NODE_ENV=production
set GENERATE_SOURCEMAP=false
npm run build

echo ✅ Production build completed successfully!
echo 📁 Build files are in client/build directory
