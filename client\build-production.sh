#!/bin/bash

echo "🚀 Building VWork Frontend for Production..."

# Remove debug/test files for production
echo "🧹 Removing debug/test files..."
rm -f src/pages/UserDebugPage.js
rm -f src/pages/TestUserStatus.js  
rm -f src/pages/LoginFlowTest.js
rm -f src/pages/ProfileSetupTest.js
rm -f src/utils/firebaseDebug.js
rm -f src/utils/backendTest.js
rm -f src/components/common/BackendDebug.js

# Check for any remaining debug imports
echo "🔍 Checking for remaining debug imports..."
if grep -r "firebaseDebug\|backendTest\|UserDebugPage\|TestUserStatus" src/ 2>/dev/null; then
    echo "⚠️  Warning: Found debug imports in source code!"
    echo "Please remove them before continuing."
    exit 1
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm ci

# Set production environment
export NODE_ENV=production
export GENERATE_SOURCEMAP=false

# Build the application
echo "🔨 Building React application..."
npm run build

echo "✅ Production build completed successfully!"
echo "📁 Build files are in build directory"
