import { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth, USER_TYPES } from '../../contexts/AuthContext';

const ProfileGuard = ({ children }) => {
  const { user, isAuthenticated, loading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // Don't check if still loading or not authenticated
    if (loading || !isAuthenticated || !user) return;

    const currentPath = location.pathname;
    console.log('🔍 ProfileGuard: Checking path:', currentPath);

    // First priority: Check email verification
    if (!user.emailVerified) {
      console.log('📧 ProfileGuard: Email not verified, redirecting...');
      navigate('/verify-email', { replace: true });
      return;
    }

    // Special handling for profile/client setup pages
    if (currentPath === '/profile-setup' || currentPath === '/client-setup') {
      // If profile is complete but user is on setup page, redirect to dashboard
      if (user.profile?.isComplete) {
        console.log('✅ ProfileGuard: Profile complete but on setup page, redirecting to dashboard...');
        navigate('/dashboard', { replace: true });
        return;
      } else {
        console.log('ℹ️ ProfileGuard: On setup page with incomplete profile - staying here');
        return;
      }
    }

    // Don't check other auth/verification pages
    const skipCheckPages = [
      '/verify-email', 
      '/login-success',
      '/simple-login-success',
      '/user-debug',
      '/test-status',
      '/check-email',
      '/login-flow'
    ];
    
    if (skipCheckPages.includes(currentPath)) return;

    // For all other pages: Check profile completion and redirect if needed
    const isProfileIncomplete = !user.profile?.isComplete;

    console.log('🔍 ProfileGuard: Profile completion check', {
      userType: user.userType,
      isComplete: user.profile?.isComplete,
      profileExists: !!user.profile,
      currentPath: currentPath,
      isProfileIncomplete
    });

    if (isProfileIncomplete) {
      console.log('👤 ProfileGuard: Profile incomplete, redirecting based on user type...');
      if (user.userType === USER_TYPES.FREELANCER) {
        navigate('/profile-setup', { replace: true });
        return;
      } else if (user.userType === USER_TYPES.CLIENT) {
        navigate('/client-setup', { replace: true });
        return;
      }
    } else {
      console.log('✅ ProfileGuard: Profile complete, allowing access to', currentPath);
    }
  }, [user, isAuthenticated, loading, navigate, location.pathname]);

  // Show loading or return children
  if (loading) {
    return (
      <div className='min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center'>
        <div className='text-center'>
          <div className='w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto' />
          <p className='mt-4 text-gray-600 dark:text-gray-400'>Loading...</p>
        </div>
      </div>
    );
  }

  return children;
};

export default ProfileGuard;
