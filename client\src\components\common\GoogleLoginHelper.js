import React from 'react';
import { ExclamationTriangleIcon, InformationCircleIcon } from '@heroicons/react/24/outline';

const GoogleLoginHelper = ({ errorCode, attempted }) => {
  if (!attempted || (!errorCode || (errorCode !== 'auth/popup-closed-by-user' && errorCode !== 'auth/popup-blocked' && errorCode !== 'auth/timeout'))) {
    return null;
  }

  const getHelperContent = () => {
    switch (errorCode) {
      case 'auth/popup-closed-by-user':
        return {
          icon: InformationCircleIcon,
          iconColor: 'text-blue-600',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          textColor: 'text-blue-800',
          title: 'Đăng nhập Google bị gián đoạn',
          tips: [
            'Nhấn nút "Đăng nhập bằng Google" để thử lại',
            '<PERSON><PERSON><PERSON> bảo hoàn thành quá trình trong cửa sổ popup',
            '<PERSON>hông đóng cửa sổ popup cho đến khi thấy thông báo thành công'
          ]
        };
      
      case 'auth/popup-blocked':
        return {
          icon: ExclamationTriangleIcon,
          iconColor: 'text-orange-600',
          bgColor: 'bg-orange-50',
          borderColor: 'border-orange-200',
          textColor: 'text-orange-800',
          title: 'Popup bị chặn bởi trình duyệt',
          tips: [
            'Tìm biểu tượng popup bị chặn trên thanh địa chỉ',
            'Nhấn vào biểu tượng và chọn "Luôn cho phép popup"',
            'Tắt trình chặn popup tạm thời',
            'Thử lại đăng nhập Google'
          ]
        };
      
      case 'auth/timeout':
        return {
          icon: ExclamationTriangleIcon,
          iconColor: 'text-red-600',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          textColor: 'text-red-800',
          title: 'Đăng nhập Google bị timeout',
          tips: [
            'Kiểm tra kết nối internet',
            'Thử lại đăng nhập Google',
            'Nếu vẫn lỗi, hãy thử đăng nhập bằng email/mật khẩu'
          ]
        };
      
      default:
        return null;
    }
  };

  const content = getHelperContent();
  if (!content) return null;

  const { icon: Icon, iconColor, bgColor, borderColor, textColor, title, tips } = content;

  return (
    <div className={`${bgColor} border ${borderColor} rounded-lg p-4 mt-4`}>
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">
          <Icon className={`w-5 h-5 ${iconColor}`} />
        </div>
        <div className={`text-sm ${textColor}`}>
          <p className="font-medium mb-2">{title}</p>
          <ul className="list-disc list-inside space-y-1">
            {tips.map((tip, index) => (
              <li key={index}>{tip}</li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default GoogleLoginHelper;
