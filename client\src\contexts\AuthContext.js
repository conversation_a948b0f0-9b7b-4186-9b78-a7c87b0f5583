import { createContext, useContext, useEffect, useState } from 'react';
import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  updateProfile,
  sendPasswordResetEmail,
  sendEmailVerification,
  applyActionCode,
  GoogleAuthProvider,
  signInWithPopup,
  reload,
  getIdToken,
} from 'firebase/auth';
import { doc, getDoc, setDoc, updateDoc, deleteDoc } from 'firebase/firestore';
import { auth, db } from '../config/firebase';
import { apiService } from '../services/api';
import { toast } from 'react-hot-toast';
import { logAuthError } from '../utils/authErrorLogger';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// User types constants
export const USER_TYPES = {
  FREELANCER: 'freelancer',
  CLIENT: 'client',
};

// Check if Firebase is properly configured
const isFirebaseConfigured = () => {
  try {
    const requiredConfig = [
      process.env.REACT_APP_FIREBASE_API_KEY,
      process.env.REACT_APP_FIREBASE_AUTH_DOMAIN,
      process.env.REACT_APP_FIREBASE_PROJECT_ID,
    ];

    const isConfigured = requiredConfig.every(config => config && config !== 'undefined');
    return isConfigured;
  } catch (error) {
    // // console.log('🔍 Firebase check error:', error.message);
    return false;
  }
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [firebaseReady, setFirebaseReady] = useState(false);
  const [debugMode, setDebugMode] = useState(false); // Disabled for production
  const [showEmailVerification, setShowEmailVerification] = useState(false);
  const [verificationEmail, setVerificationEmail] = useState('');

  // Production mode - debug disabled
  useEffect(() => {
    // Debug mode disabled in production
    if (process.env.NODE_ENV !== 'production') {
      const savedDebugMode = localStorage.getItem('vwork_debug_mode');
      if (savedDebugMode) {
        setDebugMode(savedDebugMode === 'true');
      }
    }
  }, []);

  // Toggle debug mode
  const toggleDebugMode = () => {
    const newDebugMode = !debugMode;
    setDebugMode(newDebugMode);
    localStorage.setItem('vwork_mode', newDebugMode.toString());
  };

  // Check Firebase configuration on mount
  useEffect(() => {
    const checkFirebase = () => {
      const isConfigured = isFirebaseConfigured();
      setFirebaseReady(isConfigured);

      if (!isConfigured) {
        console.log(
          '🔥 Firebase is not properly configured. Authentication features will be limited.'
        );
        console.log(
          '📋 Please check the console for Firebase configuration instructions.'
        );
        // Show a more user-friendly notification
        setTimeout(() => {
          console.log('Firebase is not configured. Please check console for instructions.');
        }, 1000);
        setLoading(false);
        return;
      }
      console.log('🔥 Firebase is ready');
    };

    checkFirebase();
  }, []);

  // Listen to authentication state changes (only if Firebase is configured)
  useEffect(() => {
    if (!firebaseReady) {
      setLoading(false);
      return;
    }

    const unsubscribe = onAuthStateChanged(auth, async firebaseUser => {
      if (firebaseUser) {
        try {
          // First, try to get user data from main users collection
          let userDoc = await getDoc(doc(db, 'users', firebaseUser.uid));
          let userData = null;
          
          if (userDoc.exists()) {
            userData = userDoc.data();
            console.log('✅ User data loaded from users collection');
          } else {
            // If not found in users, check tempUsers (for unverified users)
            console.log('🔍 Checking tempUsers collection...');
            const tempUserDoc = await getDoc(doc(db, 'tempUsers', firebaseUser.uid));
            if (tempUserDoc.exists()) {
              userData = tempUserDoc.data();
              console.log('✅ User data loaded from tempUsers collection');
              
              // If user is now verified but data is still in tempUsers, move it to users
              if (firebaseUser.emailVerified && !userData.isVerified) {
                console.log('🔄 Moving verified user data from tempUsers to users...');
                try {
                  const verifiedUserData = {
                    ...userData,
                    isVerified: true,
                    emailVerifiedAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString(),
                  };

                  // Save to main users collection
                  await setDoc(doc(db, 'users', firebaseUser.uid), verifiedUserData);
                  
                  // Delete from temporary collection
                  await deleteDoc(doc(db, 'tempUsers', firebaseUser.uid));
                  
                  userData = verifiedUserData; // Use the updated data
                  console.log('✅ User data successfully moved to main collection');
                } catch (moveError) {
                  console.error('⚠️ Failed to move user data:', moveError);
                }
              }
            } else {
              console.log('⚠️ No user data found in either collection');
              userData = {};
            }
          }

          setUser({
            uid: firebaseUser.uid,
            email: firebaseUser.email,
            displayName: firebaseUser.displayName,
            photoURL: firebaseUser.photoURL,
            emailVerified: firebaseUser.emailVerified,
            ...userData,
          });
        } catch (error) {
          // // console.log('❌ Error fetching user data from Firestore:', error);

          // Check if it's a Firestore setup issue
          if (
            error.code === 'failed-precondition' ||
            error.message?.includes('database') ||
            error.message?.includes('firestore') ||
            error.code === 'permission-denied'
          ) {
            // // console.log(
            //   '🔥 Firestore database not initialized or permission denied'
            // );
            // // console.log('📋 To fix this:');
            // // console.log('1. Go to Firebase Console → Firestore Database');
            // // console.log('2. Click "Create database"');
            // // console.log('3. Choose "Start in test mode"');
            // // console.log('4. Select location (asia-southeast1)');

            toast.error(
              'Firestore database not setup. Please follow console instructions.'
            );
          }

          // Set basic user without Firestore data
          setUser({
            uid: firebaseUser.uid,
            email: firebaseUser.email,
            displayName: firebaseUser.displayName,
            photoURL: firebaseUser.photoURL,
            emailVerified: firebaseUser.emailVerified,
            // Add some default data
            userType: 'freelancer', // Default, can be changed later
            name:
              firebaseUser.displayName ||
              firebaseUser.email?.split('@')[0] ||
              'User',
          });
        }
      } else {
        setUser(null);
      }
      setLoading(false);
    });

    return () => unsubscribe();
  }, [firebaseReady]);

  // Error message helper
  const getErrorMessage = (error) => {
    switch (error.code) {
      case 'auth/user-not-found':
        return 'Không tìm thấy tài khoản với email này.';
      case 'auth/wrong-password':
        return 'Mật khẩu không chính xác.';
      case 'auth/invalid-credential':
        return 'Thông tin đăng nhập không hợp lệ. Vui lòng kiểm tra lại email và mật khẩu.';
      case 'auth/invalid-login-credentials':
        return 'Email hoặc mật khẩu không chính xác. Vui lòng thử lại.';
      case 'auth/email-already-in-use':
        return 'Email này đã được sử dụng.';
      case 'auth/weak-password':
        return 'Mật khẩu quá yếu. Vui lòng chọn mật khẩu mạnh hơn.';
      case 'auth/invalid-email':
        return 'Email không hợp lệ.';
      case 'auth/too-many-requests':
        return 'Quá nhiều yêu cầu. Vui lòng thử lại sau ít phút.';
      case 'auth/network-request-failed':
        return 'Lỗi kết nối mạng. Vui lòng kiểm tra internet và thử lại.';
      case 'auth/requires-recent-login':
        return 'Vui lòng đăng nhập lại để thực hiện thao tác này.';
      case 'auth/invalid-action-code':
        return 'Mã xác thực không hợp lệ hoặc đã hết hạn.';
      case 'auth/expired-action-code':
        return 'Mã xác thực đã hết hạn. Vui lòng yêu cầu mã mới.';
      case 'auth/user-disabled':
        return 'Tài khoản này đã bị vô hiệu hóa.';
      case 'auth/operation-not-allowed':
        return 'Phương thức đăng nhập này chưa được kích hoạt.';
      case 'auth/account-exists-with-different-credential':
        return 'Tài khoản đã tồn tại với phương thức đăng nhập khác.';
      case 'auth/credential-already-in-use':
        return 'Thông tin đăng nhập này đã được sử dụng cho tài khoản khác.';
      case 'auth/popup-closed-by-user':
        return 'Đăng nhập bị hủy. Vui lòng thử lại.';
      case 'auth/cancelled-popup-request':
        return 'Yêu cầu đăng nhập bị hủy. Vui lòng thử lại.';
      case 'auth/popup-blocked':
        return 'Popup đăng nhập bị chặn. Vui lòng cho phép popup và thử lại.';
      case 'auth/timeout':
        return 'Yêu cầu bị timeout. Vui lòng thử lại.';
      case 'auth/missing-email':
        return 'Vui lòng nhập email.';
      case 'auth/missing-password':
        return 'Vui lòng nhập mật khẩu.';
      case 'auth/internal-error':
        return 'Lỗi hệ thống. Vui lòng thử lại sau.';
      default: {
        // Handle generic error messages
        const message = error.message || 'Đã xảy ra lỗi. Vui lòng thử lại.';
        
        // Clean up Firebase error messages for better UX
        if (message.includes('invalid-credential')) {
          return 'Thông tin đăng nhập không hợp lệ. Vui lòng kiểm tra lại email và mật khẩu.';
        }
        if (message.includes('user-not-found')) {
          return 'Không tìm thấy tài khoản với email này.';
        }
        if (message.includes('wrong-password')) {
          return 'Mật khẩu không chính xác.';
        }
        if (message.includes('too-many-requests')) {
          return 'Quá nhiều yêu cầu. Vui lòng thử lại sau ít phút.';
        }
        if (message.includes('network')) {
          return 'Lỗi kết nối mạng. Vui lòng kiểm tra internet và thử lại.';
        }
        
        return message;
      }
    }
  };

  // Show email verification modal
  const showEmailVerificationModal = (email) => {
    setVerificationEmail(email);
    setShowEmailVerification(true);
  };

  // Handle resend verification
  const handleResendVerification = async () => {
    await sendVerificationEmail();
    return { success: true };
  };

  // Handle continue after verification
  const handleContinueVerification = () => {
    setShowEmailVerification(false);
    setVerificationEmail('');
  };

  const register = async (email, password, userData) => {
    if (!firebaseReady) {
      toast.error(
        'Firebase is not configured. Please check console for instructions.'
      );
      return { success: false, error: 'Firebase not configured' };
    }

    try {
      setLoading(true);

      // Enhanced validation
      if (!email || !email.trim()) {
        throw new Error('Email is required');
      }

      if (!password || password.length < 6) {
        throw new Error('Password must be at least 6 characters long');
      }

      if (password.length > 128) {
        throw new Error('Password too long (max 128 characters)');
      }

      // Check for weak passwords
      const weakPasswords = ['123456', 'password', '123456789', 'qwerty', 'abc123', 'password123'];
      if (weakPasswords.includes(password.toLowerCase())) {
        throw new Error('Password too weak. Please choose a stronger password.');
      }

      if (!userData.userType ||
          !Object.values(USER_TYPES).includes(userData.userType)) {
        throw new Error('Valid user type is required');
      }

      if (!userData.name || userData.name.trim().length < 2) {
        throw new Error('Name must be at least 2 characters long');
      }

      if (userData.name.trim().length > 100) {
        throw new Error('Name too long (max 100 characters)');
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        throw new Error('Please enter a valid email address');
      }

      // Check if email is too long
      if (email.length > 254) {
        throw new Error('Email too long (max 254 characters)');
      }

      console.log('🔍 Attempting registration with:', { 
        email, 
        passwordLength: password.length,
        userType: userData.userType,
        name: userData.name
      });

      // Create user account with Firebase
      const { user: firebaseUser } = await createUserWithEmailAndPassword(
        auth,
        email,
        password
      );

      // Update Firebase profile
      await updateProfile(firebaseUser, {
        displayName: userData.name,
      });

      // Get Firebase ID token for backend
      const firebaseToken = await getIdToken(firebaseUser);

      // Send email verification
      await sendEmailVerification(firebaseUser, {
        url: `${window.location.origin}/verify-email?continueUrl=${encodeURIComponent(window.location.origin + '/dashboard')}`,
        handleCodeInApp: false,
      });

      // Register with backend API
      try {
        const backendResponse = await apiService.auth.register({
          firebaseToken,
          userType: userData.userType,
          name: userData.name,
          guildSpecialization: userData.guildSpecialization || ''
        });

        console.log('✅ Backend registration successful:', backendResponse);
      } catch (backendError) {
        console.error('⚠️ Backend registration failed:', backendError);
        // Continue with Firebase-only flow if backend fails
        toast.error('Account created but backend sync failed. Please try again later.');
      }

      // Store temporary user data (not in main users collection)
      const tempUserDocData = {
        name: userData.name,
        email: email,
        userType: userData.userType,
        guildRank: userData.userType === USER_TYPES.CLIENT ? 'lord' : 'apprentice',
        guildTitle: userData.userType === USER_TYPES.CLIENT ? 'Noble Client' : 'New Member',
        
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        isActive: true,
        isVerified: false,
        emailVerificationSent: true,
        profile: {
          bio: '',
          avatar: firebaseUser.photoURL || null,
          location: {
            country: '',
            city: '',
            timezone: '',
          },
          website: '',
          phoneNumber: '',
          skills: [],
          hourlyRate: userData.userType === USER_TYPES.FREELANCER ? 0 : null,
          availability: userData.userType === USER_TYPES.FREELANCER ? 'available' : null,
          isComplete: false,
        },
        stats: {
          totalProjects: 0,
          completedProjects: 0,
          totalEarnings: 0,
          averageRating: 0,
          totalReviews: 0,
          responseTime: 24,
          completionRate: 100,
        },
        preferences: {
          emailNotifications: true,
          pushNotifications: true,
    
          theme: 'medieval',
          language: 'vi',
        },
      };

      // Save to temporary collection until email is verified
      try {
        await setDoc(doc(db, 'tempUsers', firebaseUser.uid), tempUserDocData);
        console.log('✅ Temporary user data saved successfully');
      } catch (firestoreError) {
        console.error('⚠️ Failed to save temporary user data:', firestoreError);
        toast.error('Account created but some data may not be saved. Please verify your email.');
      }

      // Show email verification notification
      showEmailVerificationModal(email);
      
      return {
        success: true,
        userType: userData.userType,
        needsEmailVerification: true,
        email: email,
      };
    } catch (error) {
      // Enhanced error logging with context
      const errorContext = logAuthError(error, {
        action: 'register',
        email: email,
        userType: userData.userType,
        nameLength: userData.name.length,
        emailLength: email.length,
        passwordLength: password.length,
        hasNumbers: /\d/.test(password),
        hasSymbols: /[^a-zA-Z0-9]/.test(password)
      });
      
      // Additional error context for debugging
      if (error.code === 'auth/email-already-in-use') {
        console.error('🔍 Email already in use - user may have forgotten they have an account');
      } else if (error.code === 'auth/weak-password') {
        console.error('🔍 Weak password - suggest stronger password requirements');
      } else if (error.code === 'auth/invalid-email') {
        console.error('🔍 Invalid email format provided');
      }
      
      const message = getErrorMessage(error);
      toast.error(message);
      return { success: false, error: message, errorCode: error.code, errorContext };
    } finally {
      setLoading(false);
    }
  };

  // Send email verification
  const sendVerificationEmail = async () => {
    if (!firebaseReady) {
      toast.error('Firebase is not configured.');
      return { success: false, error: 'Firebase not configured' };
    }

    try {
      setLoading(true);
      
      if (!auth.currentUser) {
        throw new Error('No user is currently signed in');
      }

      if (auth.currentUser.emailVerified) {
        toast.info('Email đã được xác thực rồi.');
        return { success: true, message: 'Email already verified' };
      }

      await sendEmailVerification(auth.currentUser, {
        url: `${window.location.origin}/verify-email?continueUrl=${encodeURIComponent(window.location.origin + '/dashboard')}`,
        handleCodeInApp: false,
      });

      toast.success('Email xác thực đã được gửi! Vui lòng kiểm tra hộp thư.');
      return { success: true };
    } catch (error) {
      console.error('🔍 Send verification email error:', error);
      const message = getErrorMessage(error);
      toast.error(message);
      return { success: false, error: message };
    } finally {
      setLoading(false);
    }
  };

  // Verify email with action code
  const verifyEmail = async (actionCode) => {
    if (!firebaseReady) {
      toast.error('Firebase is not configured.');
      return { success: false, error: 'Firebase not configured' };
    }

    try {
      setLoading(true);

      // Apply the email verification code
      await applyActionCode(auth, actionCode);

      // Reload the user to get updated emailVerified status
      if (auth.currentUser) {
        await reload(auth.currentUser);
      }

      // Move data from tempUsers to users collection
      if (auth.currentUser && auth.currentUser.emailVerified) {
        try {
          // Get temporary user data
          const tempUserDoc = await getDoc(doc(db, 'tempUsers', auth.currentUser.uid));
          
          if (tempUserDoc.exists()) {
            const tempUserData = tempUserDoc.data();
            
            // Update verification status
            const verifiedUserData = {
              ...tempUserData,
              isVerified: true,
              emailVerifiedAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            };

            // Save to main users collection
            await setDoc(doc(db, 'users', auth.currentUser.uid), verifiedUserData);
            
            // Delete from temporary collection
            await deleteDoc(doc(db, 'tempUsers', auth.currentUser.uid));
            
            console.log('✅ User data moved to main collection after verification');
          }
        } catch (firestoreError) {
          console.error('⚠️ Failed to move user data after verification:', firestoreError);
        }
      }

      toast.success('Email đã được xác thực thành công!');
      return { success: true };
    } catch (error) {
      console.error('🔍 Email verification error:', error);
      const message = getErrorMessage(error);
      toast.error(message);
      return { success: false, error: message };
    } finally {
      setLoading(false);
    }
  };

  // Reset password
  const resetPassword = async (email) => {
    if (!firebaseReady) {
      toast.error('Firebase is not configured.');
      return { success: false, error: 'Firebase not configured' };
    }

    try {
      setLoading(true);

      if (!email || !email.trim()) {
        throw new Error('Email is required');
      }

      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        throw new Error('Please enter a valid email address');
      }

      await sendPasswordResetEmail(auth, email, {
        url: `${window.location.origin}/login`,
        handleCodeInApp: false,
      });

      toast.success('Email đặt lại mật khẩu đã được gửi!');
      return { success: true };
    } catch (error) {
      console.error('🔍 Password reset error:', error);
      const message = getErrorMessage(error);
      toast.error(message);
      return { success: false, error: message };
    } finally {
      setLoading(false);
    }
  };

  const login = async (email, password) => {
    if (!firebaseReady) {
      toast.error(
        'Firebase is not configured. Please check console for instructions.'
      );
      return { success: false, error: 'Firebase not configured' };
    }

    try {
      setLoading(true);

      // Enhanced validation
      if (!email || !email.trim()) {
        throw new Error('Email is required');
      }

      if (!password || password.length < 1) {
        throw new Error('Password is required');
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        throw new Error('Please enter a valid email address');
      }

      console.log('🔍 Attempting login with:', { email, passwordLength: password.length });
      
      const { user: firebaseUser } = await signInWithEmailAndPassword(auth, email, password);
      
      // Check if email is verified
      if (!firebaseUser.emailVerified) {
        toast.error('Vui lòng xác thực email trước khi đăng nhập.');
        await signOut(auth);
        return { 
          success: false, 
          error: 'Email not verified',
          needsEmailVerification: true,
          email: email
        };
      }

      // Get Firebase ID token for backend
      const firebaseToken = await getIdToken(firebaseUser);

      // Login with backend API
      try {
        const backendResponse = await apiService.auth.login({
          firebaseToken
        });

        console.log('✅ Backend login successful:', backendResponse);
        
        // Store backend token if provided
        if (backendResponse.data?.token) {
          localStorage.setItem('backend_token', backendResponse.data.token);
        }
      } catch (backendError) {
        console.error('⚠️ Backend login failed:', backendError);
        // Continue with Firebase-only flow if backend fails
        toast.error('Login successful but backend sync failed. Some features may be limited.');
      }

      toast.success('Chào mừng bạn trở lại VWork Guild!');
      
      // Redirect to login success handler for status check
      window.location.href = '/login-success';
      
      return { success: true };
    } catch (error) {
      // Enhanced error logging with context
      const errorContext = logAuthError(error, {
        action: 'login',
        email: email,
        emailLength: email.length,
        passwordLength: password.length,
        hasNumbers: /\d/.test(password),
        hasSymbols: /[^a-zA-Z0-9]/.test(password)
      });
      
      // Additional error context for debugging
      if (error.code === 'auth/invalid-credential') {
        console.error('🔍 Invalid credential error - possible causes:');
        console.error('1. Incorrect email or password');
        console.error('2. Account does not exist');
        console.error('3. Account disabled or deleted');
        console.error('4. Network connectivity issues');
      }
      
      const message = getErrorMessage(error);
      toast.error(message);
      return { success: false, error: message, errorCode: error.code, errorContext };
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    if (!firebaseReady) {
      return { success: false, error: 'Firebase not configured' };
    }

    try {
      setLoading(true);

      // Logout from backend API
      try {
        await apiService.auth.logout();
        console.log('✅ Backend logout successful');
      } catch (backendError) {
        console.error('⚠️ Backend logout failed:', backendError);
      }

      // Clear backend token
      localStorage.removeItem('backend_token');

      // Logout from Firebase
      await signOut(auth);

      toast.success('Đã đăng xuất thành công!');
      return { success: true };
    } catch (error) {
      console.error('🔍 Logout Error:', error);
      const message = getErrorMessage(error);
      toast.error(message);
      return { success: false, error: message };
    } finally {
      setLoading(false);
    }
  };

  const updateUserProfile = async profileData => {
    if (!firebaseReady) {
      toast.error('Firebase is not configured.');
      return { success: false, error: 'Firebase not configured' };
    }

    try {
      setLoading(true);

      if (!user) throw new Error('No user logged in');

      // Update Firestore document
      const userRef = doc(db, 'users', user.uid);
      const updateData = {
        ...profileData,
        updatedAt: new Date().toISOString(),
      };

      await updateDoc(userRef, updateData);

      // Update Firebase profile if name changed
      if (profileData.name && profileData.name !== user.displayName) {
        await updateProfile(auth.currentUser, {
          displayName: profileData.name,
        });
      }

      // **FIX: Update the user state in memory to reflect the changes**
      setUser(prevUser => ({
        ...prevUser,
        ...updateData,
        displayName: profileData.name || prevUser.displayName
      }));

      console.log('✅ User profile updated in both Firestore and local state');
      toast.success('Guild profile updated successfully!');
      return { success: true };
    } catch (error) {
      const message = getErrorMessage(error);
      toast.error(message);
      return { success: false, error: message };
    } finally {
      setLoading(false);
    }
  };

  const loginWithGoogle = async userType => {
    if (!firebaseReady) {
      toast.error(
        'Firebase is not configured. Please check console for instructions.'
      );
      return { success: false, error: 'Firebase not configured' };
    }

    try {
      setLoading(true);
      const provider = new GoogleAuthProvider();
      const { user: firebaseUser } = await signInWithPopup(auth, provider);

      // Google accounts are automatically verified
      if (firebaseUser.emailVerified) {
        // Get Firebase ID token for backend
        const firebaseToken = await getIdToken(firebaseUser);

        // Login with backend API
        try {
          const backendResponse = await apiService.auth.login({
            firebaseToken
          });

          console.log('✅ Backend Google login successful:', backendResponse);
          
          // Store backend token if provided
          if (backendResponse.data?.token) {
            localStorage.setItem('backend_token', backendResponse.data.token);
          }
        } catch (backendError) {
          console.error('⚠️ Backend Google login failed:', backendError);
          // Continue with Firebase-only flow if backend fails
          toast.error('Google login successful but backend sync failed. Some features may be limited.');
        }

        // Check if user already exists in Firestore
        try {
          const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid));

          if (!userDoc.exists()) {
            // New user - save to Firestore with selected user type
            const userDocData = {
              name: firebaseUser.displayName || 'User',
              email: firebaseUser.email,
              userType: userType,
              guildRank: userType === USER_TYPES.CLIENT ? 'lord' : 'apprentice',
              guildTitle: userType === USER_TYPES.CLIENT ? 'Noble Client' : 'New Member',
              
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              isActive: true,
              isVerified: true, // Google accounts are verified
              emailVerifiedAt: new Date().toISOString(),
              profile: {
                bio: '',
                avatar: firebaseUser.photoURL || null,
                location: {
                  country: '',
                  city: '',
                  timezone: '',
                },
                website: '',
                phoneNumber: '',
                skills: [],
                hourlyRate: userType === USER_TYPES.FREELANCER ? 0 : null,
                availability: userType === USER_TYPES.FREELANCER ? 'available' : null,
              },
              stats: {
                totalProjects: 0,
                completedProjects: 0,
                totalEarnings: 0,
                averageRating: 0,
                totalReviews: 0,
                responseTime: 24,
                completionRate: 100,
              },
              preferences: {
                emailNotifications: true,
                pushNotifications: true,
                marketingEmails: false,
                theme: 'medieval',
                language: 'vi',
              },
            };

            await setDoc(doc(db, 'users', firebaseUser.uid), userDocData);
            console.log('✅ New Google user saved to Firestore');
          }
        } catch (firestoreError) {
          console.error('⚠️ Failed to save Google user data:', firestoreError);
          toast.error('Đăng nhập thành công nhưng có thể có lỗi khi lưu dữ liệu.');
        }
      }

      toast.success('Đăng nhập Google thành công!');
      return { success: true, userType };
    } catch (error) {
      // Enhanced error logging with context
      const errorContext = logAuthError(error, {
        action: 'googleLogin',
        userType: userType
      });
      
      // Additional error context for debugging
      if (error.code === 'auth/popup-closed-by-user') {
        console.error('🔍 User closed the Google login popup');
      } else if (error.code === 'auth/cancelled-popup-request') {
        console.error('🔍 Google login popup was cancelled');
      } else if (error.code === 'auth/popup-blocked') {
        console.error('🔍 Google login popup was blocked by browser');
      }
      
      const message = getErrorMessage(error);
      toast.error(message);
      return { success: false, error: message, errorCode: error.code, errorContext };
    } finally {
      setLoading(false);
    }
  };

  const value = {
    user,
    loading,
    isAuthenticated: !!user,
    firebaseReady,
    debugMode,
    toggleDebugMode,
    register,
    login,
    loginWithGoogle,
    logout,
    sendVerificationEmail,
    verifyEmail,
    resetPassword,
    updateUserProfile,
    USER_TYPES,
    showEmailVerification,
    verificationEmail,
    handleResendVerification,
    handleContinueVerification,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
