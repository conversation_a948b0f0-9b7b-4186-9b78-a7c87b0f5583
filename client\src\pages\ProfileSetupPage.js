import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { gsap } from 'gsap';
import { useAuth, USER_TYPES } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import { toast } from 'react-hot-toast';
import {
  BriefcaseIcon,
  AcademicCapIcon,
  ClockIcon,
  DocumentTextIcon,
  CheckCircleIcon,
  ShieldCheckIcon,
  StarIcon,
  ArrowRightIcon,
  ArrowLeftIcon,
} from '@heroicons/react/24/outline';
import { ApplePageWrapper } from '../components/apple';

const ProfileSetupPage = () => {
  const navigate = useNavigate();
  const { user, updateUserProfile } = useAuth();
  const { t } = useLanguage();

  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 6;

  const [formData, setFormData] = useState({
    skillLevel: '',
    workExperience: '',
    yearsOfExperience: '',
    major: '',
    bio: '',
    skills: [],
  });

  const [errors, setErrors] = useState({});

  const containerRef = useRef(null);
  const titleRef = useRef(null);
  const formRef = useRef(null);
  const progressRef = useRef(null);

  // Redirect if not freelancer or profile already complete
  useEffect(() => {
    if (!user) {
      navigate('/login');
      return;
    }

    console.log('🔍 ProfileSetupPage: User check', {
      userType: user.userType,
      profileComplete: user.profile?.isComplete,
      expectedType: USER_TYPES.FREELANCER
    });

    if (user.userType && user.userType !== USER_TYPES.FREELANCER) {
      console.log('⚠️ ProfileSetupPage: Not a freelancer, redirecting to dashboard');
      navigate('/dashboard');
      return;
    }

    // Check if profile is already complete
    if (user.profile?.isComplete) {
      console.log('✅ ProfileSetupPage: Profile already complete, redirecting to dashboard');
      navigate('/dashboard');
      return;
    }
  }, [user, navigate]);

  // Medieval GSAP Animations
  useEffect(() => {
    const tl = gsap.timeline();

    tl.fromTo(
      titleRef.current,
      { opacity: 0, y: -50, rotationX: 15 },
      { opacity: 1, y: 0, rotationX: 0, duration: 1.2, ease: 'power3.out' }
    )
      .fromTo(
        progressRef.current,
        { opacity: 0, scaleX: 0, transformOrigin: 'left center' },
        { opacity: 1, scaleX: 1, duration: 0.8, ease: 'power2.out' },
        '-=0.6'
      )
      .fromTo(
        formRef.current,
        { opacity: 0, y: 30, scale: 0.95 },
        { opacity: 1, y: 0, scale: 1, duration: 0.8, ease: 'power3.out' },
        '-=0.4'
      );
  }, [currentStep]);

  // Apple-style skill data
  const skillLevels = [
    {
      value: 'beginner',
      label: '🌱 ' + t('beginner'),
      description: t('newToField'),
    },
    {
      value: 'intermediate',
      label: '⭐ ' + t('intermediate'),
      description: t('experiencedProfessional'),
    },
    {
      value: 'expert',
      label: '🏆 ' + t('expert'),
      description: t('industryLeader'),
    },
  ];

  const workExperiences = [
    {
      value: 'less-than-1',
      label: '🌱 ' + t('entryLevel'),
      description: t('lessThan1Year'),
    },
    {
      value: '1-3',
      label: '⭐ ' + t('juniorProfessional'),
      description: t('1to3Years'),
    },
    {
      value: '3-5',
      label: '🚀 ' + t('midLevelExpert'),
      description: t('3to5Years'),
    },
    {
      value: '5-10',
      label: '🏆 ' + t('seniorProfessional'),
      description: t('5to10Years'),
    },
    {
      value: 'more-than-10',
      label: '👑 ' + t('industryLeader'),
      description: t('over10Years'),
    },
  ];

  const majors = [
    { value: 'web-development', label: '🌐 ' + t('webDevelopment') },
    { value: 'backend-development', label: '⚗️ ' + t('backendDevelopment') },
    { value: 'mobile-development', label: '📱 ' + t('mobileDevelopment') },
    { value: 'design', label: '🎨 ' + t('design') },
    { value: 'data-analytics', label: '📊 ' + t('dataAnalytics') },
    { value: 'digital-marketing', label: '📢 ' + t('digitalMarketing') },
    { value: 'content-writing', label: '📝 ' + t('contentWriting') },
    { value: 'translation', label: '🗣️ ' + t('translation') },
    { value: 'project-management', label: '👥 ' + t('projectManagement') },
    { value: 'other', label: '🔮 ' + t('other') },
  ];

  const skillSuggestions = {
    'web-development': [
      'React Spells',
      'Vue Enchantments',
      'Angular Runes',
      'JavaScript Magic',
      'CSS Artistry',
      'HTML Foundations',
    ],
    'backend-development': [
      'Node.js Potions',
      'Python Sorcery',
      'Java Incantations',
      'Database Scrolls',
      'API Crafting',
      'Cloud Magic',
    ],
    'mobile-development': [
      'iOS Swift Magic',
      'Android Kotlin',
      'React Native',
      'Flutter Spells',
      'Mobile UI Arts',
    ],
    'design': [
      'Photoshop Mastery',
      'Illustrator Arts',
      'Figma Magic',
      'UI/UX Design',
      'Brand Identity',
      'Color Theory',
    ],
    'data-analytics': [
      'Python Analytics',
      'R Sorcery',
      'SQL Queries',
      'Machine Learning',
      'Data Visualization',
      'Statistics',
    ],
    'digital-marketing': [
      'SEO Magic',
      'Social Media Arts',
      'Content Strategy',
      'Email Campaigns',
      'Analytics Tracking',
    ],
    'content-writing': [
      'Creative Writing',
      'Technical Documentation',
      'Blog Crafting',
      'Copywriting',
      'SEO Writing',
    ],
    'translation': [
      'English',
      'Spanish',
      'French',
      'German',
      'Chinese',
      'Japanese',
      'Korean',
    ],
    'project-management': [
      'Team Leadership',
      'Agile Methods',
      'Scrum Master',
      'Project Planning',
      'Risk Management',
    ],
    'other': [],
  };

  // Form handlers
  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));

    // Clear errors for this field
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: '',
      }));
    }
  };

  const handleSkillToggle = skill => {
    setFormData(prev => ({
      ...prev,
      skills: prev.skills.includes(skill)
        ? prev.skills.filter(s => s !== skill)
        : [...prev.skills, skill],
    }));
  };

  const validateStep = () => {
    const newErrors = {};

    switch (currentStep) {
      case 1:
        if (!formData.skillLevel)
          newErrors.skillLevel = 'Please select your mastery level';
        break;
      case 2:
        if (!formData.workExperience)
          newErrors.workExperience = 'Please select your experience';
        break;
      case 3:
        if (!formData.major)
          newErrors.major = 'Please select your craft specialty';
        break;
      case 4:
        if (formData.skills.length === 0)
          newErrors.skills = 'Please select at least one skill';
        break;
      case 5:
        if (!formData.bio || formData.bio.trim().length < 50) {
          newErrors.bio =
            'Please write at least 50 characters about your journey';
        }
        break;
      default:
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const nextStep = () => {
    if (validateStep()) {
      setCurrentStep(prev => Math.min(prev + 1, totalSteps));
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async () => {
    if (!validateStep()) return;

    setLoading(true);
    setErrors({}); // Clear previous errors
    
    try {
      console.log('🔄 Updating user profile with form data:', formData);
      
      // Update user profile with form data
      const profileData = {
        profile: {
          ...user.profile, // Keep existing profile data
          ...formData, // Add new form data
          isComplete: true,
          completedAt: new Date().toISOString(),
        },
        updatedAt: new Date().toISOString(),
      };

      console.log('🚀 Submitting profile data to backend:', profileData);
      const result = await updateUserProfile(profileData);
      
      if (result.success) {
        console.log('✅ Profile setup completed successfully');
        console.log('🔍 User state should now have profile.isComplete = true');
        toast.success('Profile setup completed! Welcome to VWork!');
        
        // Force a small delay to ensure state is updated
        setTimeout(() => {
          console.log('🚀 Redirecting to dashboard...');
          navigate('/dashboard', { replace: true });
        }, 1000);
      } else {
        console.error('❌ Profile update failed:', result.error);
        setErrors({ submit: result.error || 'Failed to update profile' });
      }
    } catch (error) {
      console.error('❌ Profile setup error:', error);
      setErrors({ submit: error.message || 'An error occurred during profile setup' });
    } finally {
      setLoading(false);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className='space-y-6'>
            <div className='text-center mb-8'>
              <div className='w-16 h-16 mx-auto mb-4 bg-blue-500 rounded-2xl flex items-center justify-center shadow-lg'>
                <StarIcon className='h-8 w-8 text-white' />
              </div>
              <h3 className='text-xl font-bold text-gray-900 dark:text-gray-100'>
                {t('experienceLevel')}
              </h3>
              <p className='text-gray-600 dark:text-gray-400 mt-2'>
                {t('whatLevelOfExpertise')}
              </p>
            </div>

            <div className='space-y-4'>
              {skillLevels.map(level => (
                <div
                  key={level.value}
                  onClick={() => handleInputChange('skillLevel', level.value)}
                  className={`p-4 cursor-pointer transition-all duration-300 rounded-xl border-2 ${
                    formData.skillLevel === level.value
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 shadow-sm'
                      : 'border-gray-200 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-400 hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}
                >
                  <div className='flex items-center justify-between'>
                    <div>
                      <h4
                        className={`font-semibold ${
                          formData.skillLevel === level.value
                            ? 'text-gray-900 dark:text-gray-100'
                            : 'text-gray-700 dark:text-gray-300'
                        }`}
                      >
                        {level.label}
                      </h4>
                      <p
                        className={`text-sm mt-1 ${
                          formData.skillLevel === level.value
                            ? 'text-gray-700 dark:text-gray-300'
                            : 'text-gray-600 dark:text-gray-400'
                        }`}
                      >
                        {level.description}
                      </p>
                    </div>
                    <div
                      className={`
                      w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all duration-300
                      ${
                        formData.skillLevel === level.value
                          ? 'border-blue-500 bg-blue-500'
                          : 'border-gray-300 dark:border-gray-500'
                      }
                    `}
                    >
                      {formData.skillLevel === level.value && (
                        <CheckCircleIcon className='w-4 h-4 text-white' />
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {errors.skillLevel ? (
              <p className='text-red-600 dark:text-red-400 text-sm'>{errors.skillLevel}</p>
            ) : null}
          </div>
        );

      case 2:
        return (
          <div className='space-y-6'>
            <div className='text-center mb-8'>
              <div className='w-16 h-16 mx-auto mb-4 bg-blue-500 rounded-2xl flex items-center justify-center shadow-lg'>
                <ClockIcon className='h-8 w-8 text-white' />
              </div>
              <h3 className='text-xl font-bold text-gray-900 dark:text-gray-100'>
                {t('workExperience')}
              </h3>
              <p className='text-gray-600 dark:text-gray-400 mt-2'>
                {t('howLongWorking')}
              </p>
            </div>

            <div className='space-y-4'>
              {workExperiences.map(exp => (
                <div
                  key={exp.value}
                  onClick={() => handleInputChange('workExperience', exp.value)}
                  className={`p-4 cursor-pointer transition-all duration-300 rounded-xl border-2 ${
                    formData.workExperience === exp.value
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 shadow-sm'
                      : 'border-gray-200 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-400 hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}
                >
                  <div className='flex items-center justify-between'>
                    <div>
                      <h4
                        className={`font-semibold ${
                          formData.workExperience === exp.value
                            ? 'text-gray-900 dark:text-gray-100'
                            : 'text-gray-700 dark:text-gray-300'
                        }`}
                      >
                        {exp.label}
                      </h4>
                      <p
                        className={`text-sm mt-1 ${
                          formData.workExperience === exp.value
                            ? 'text-gray-700 dark:text-gray-300'
                            : 'text-gray-600 dark:text-gray-400'
                        }`}
                      >
                        {exp.description}
                      </p>
                    </div>
                    <div
                      className={`
                      w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all duration-300
                      ${
                        formData.workExperience === exp.value
                          ? 'border-blue-500 bg-blue-500'
                          : 'border-gray-300 dark:border-gray-500'
                      }
                    `}
                    >
                      {formData.workExperience === exp.value && (
                        <CheckCircleIcon className='w-4 h-4 text-white' />
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {errors.workExperience ? (
              <p className='text-red-600 dark:text-red-400 text-sm'>{errors.workExperience}</p>
            ) : null}
          </div>
        );

      case 3:
        return (
          <div className='space-y-6'>
            <div className='text-center mb-8'>
              <div className='w-16 h-16 mx-auto mb-4 bg-blue-500 rounded-2xl flex items-center justify-center shadow-lg'>
                <AcademicCapIcon className='h-8 w-8 text-white' />
              </div>
              <h3 className='text-xl font-bold text-gray-900 dark:text-gray-100'>
                {t('craftSpecialty')}
              </h3>
              <p className='text-gray-600 dark:text-gray-400 mt-2'>
                {t('whatMysticalArts')}
              </p>
            </div>

            <div>
              <label className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3'>
                {t('primaryGuildArt')}
              </label>
              <select
                value={formData.major}
                onChange={e => handleInputChange('major', e.target.value)}
                className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 ${
                  errors.major ? 'border-red-500' : 'border-gray-200 dark:border-gray-600'
                }`}
              >
                <option value=''>{t('choosePrimaryCraft')}</option>
                {majors.map(major => (
                  <option key={major.value} value={major.value}>
                    {major.label}
                  </option>
                ))}
              </select>

              {errors.major ? (
                <p className='mt-2 text-red-600 dark:text-red-400 text-sm'>
                  {errors.major}
                </p>
              ) : null}
            </div>
          </div>
        );

      case 4: {
        const currentSkills = skillSuggestions[formData.major] || [];

        return (
          <div className='space-y-6'>
            <div className='text-center mb-8'>
              <div className='w-16 h-16 mx-auto mb-4 bg-blue-500 rounded-2xl flex items-center justify-center shadow-lg'>
                <BriefcaseIcon className='h-8 w-8 text-white' />
              </div>
              <h3 className='text-xl font-bold text-gray-900 dark:text-gray-100'>
                {t('magicalArsenal')}
              </h3>
              <p className='text-gray-600 dark:text-gray-400 mt-2'>
                {t('selectSpellsAbilities')}
              </p>
            </div>

            {currentSkills.length > 0 ? (
              <div>
                <label className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3'>
                  {t('recommendedSkills')}
                  {majors.find(m => m.value === formData.major)?.label}
                </label>
                <div className='grid grid-cols-2 gap-3'>
                  {currentSkills.map(skill => (
                    <button
                      key={skill}
                      type='button'
                      onClick={() => handleSkillToggle(skill)}
                      className={`p-3 rounded-lg border-2 transition-all duration-300 text-sm ${
                        formData.skills.includes(skill)
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                          : 'border-gray-300 dark:border-gray-600 hover:border-blue-400 dark:hover:border-blue-400 text-gray-700 dark:text-gray-300'
                      }`}
                    >
                      {formData.skills.includes(skill) ? '✨ ' : '⚪ '}
                      {skill}
                    </button>
                  ))}
                </div>
              </div>
            ) : (
              <div className='text-center py-8'>
                <p className='text-gray-600 dark:text-gray-400 mb-4'>
                  {t('pleaseSelectCraftFirst')}
                </p>
                <button
                  onClick={() => setCurrentStep(3)}
                  className='px-6 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-all duration-200'
                >
                  {t('returnToChooseCraft')}
                </button>
              </div>
            )}

            {errors.skills ? (
              <p className='text-red-600 dark:text-red-400 text-sm'>
                {errors.skills}
              </p>
            ) : null}
          </div>
        );
      }

      case 5:
        return (
          <div className='space-y-6'>
            <div className='text-center mb-8'>
              <div className='w-16 h-16 mx-auto mb-4 bg-blue-500 rounded-2xl flex items-center justify-center shadow-lg'>
                <DocumentTextIcon className='h-8 w-8 text-white' />
              </div>
              <h3 className='text-xl font-bold text-gray-900 dark:text-gray-100'>
                {t('yourStory')}
              </h3>
              <p className='text-gray-600 dark:text-gray-400 mb-6'>
                {t('tellYourJourney')}
              </p>
            </div>

            <div>
              <label className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3'>
                {t('yourEpicStory')}
              </label>
              <textarea
                value={formData.bio}
                onChange={e => handleInputChange('bio', e.target.value)}
                rows={6}
                className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 resize-none ${
                  errors.bio ? 'border-red-500' : 'border-gray-200 dark:border-gray-600'
                }`}
                placeholder={t('writeAboutYourJourney')}
              />
              <div className='flex justify-between items-center mt-2'>
                <span
                  className={`text-xs ${
                    formData.bio.length >= 50
                      ? 'text-green-600 dark:text-green-400'
                      : 'text-gray-500 dark:text-gray-400'
                  }`}
                >
                  {formData.bio.length}/50 characters
                </span>
              </div>

              {errors.bio ? (
                <p className='mt-2 text-red-600 dark:text-red-400 text-sm'>
                  {errors.bio}
                </p>
              ) : null}
            </div>
          </div>
        );

      case 6:
        return (
          <div className='space-y-6'>
            <div className='text-center mb-8'>
              <div className='w-16 h-16 mx-auto mb-4 bg-green-500 rounded-2xl flex items-center justify-center shadow-lg'>
                <CheckCircleIcon className='h-8 w-8 text-white' />
              </div>
              <h3 className='text-xl font-bold text-gray-900 dark:text-gray-100'>
                {t('registrationComplete')}
              </h3>
              <p className='text-gray-600 dark:text-gray-400 mb-6'>
                {t('accountCreatedSuccessfully')}
              </p>
            </div>

            <div className='bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-xl p-6'>
              <h4 className='font-semibold text-green-800 dark:text-green-200 mb-4'>
                Profile Setup Complete!
              </h4>
              <div className='space-y-3 text-sm'>
                <p className='text-gray-700 dark:text-gray-300'>
                  Your freelancer profile has been successfully created. You will be redirected to the dashboard shortly.
                </p>
                <div className='mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg'>
                  <p className='text-blue-800 dark:text-blue-200 text-sm'>
                    Welcome to VWork! Start browsing projects and build your portfolio.
                  </p>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <ApplePageWrapper
      ref={containerRef}
      className='py-12 px-4 sm:px-6 lg:px-8'
      variant='page'
    >
      <div className='max-w-2xl mx-auto'>
        {/* Apple-style Header */}
        <div ref={titleRef} className='text-center mb-12'>
          <div className='w-16 h-16 mx-auto mb-6 bg-blue-500 rounded-2xl flex items-center justify-center shadow-lg'>
            <ShieldCheckIcon className='h-8 w-8 text-white' />
          </div>
          <h1 className='text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4'>
            {t('completeYourProfile')}
          </h1>
          <p className='text-lg text-gray-600 dark:text-gray-400 mb-8 leading-relaxed'>
            {t('setUpYourProfessionalProfile')}
          </p>
        </div>

        {/* Apple-style Progress Bar */}
        <div ref={progressRef} className='mb-12'>
          <div className='flex justify-between items-center mb-4'>
            <span className='font-medium text-gray-700 dark:text-gray-300'>
              {t('step')} {currentStep} {t('of')} {totalSteps}
            </span>
            <span className='text-sm text-gray-600 dark:text-gray-400'>
              {Math.round((currentStep / totalSteps) * 100)}% {t('complete')}
            </span>
          </div>
          <div className='w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 overflow-hidden'>
            <div
              className='bg-gradient-to-r from-blue-500 to-blue-600 h-full rounded-full transition-all duration-500 ease-out'
              style={{ width: `${(currentStep / totalSteps) * 100}%` }}
            />
          </div>
        </div>

        {/* Apple-style Form */}
        <div
          ref={formRef}
          className='bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-8 mb-8'
        >
          {renderStep()}
        </div>

        {/* Apple-style Navigation Buttons */}
        <div className='flex justify-between items-center'>
          <button
            onClick={prevStep}
            disabled={currentStep === 1}
            className={`inline-flex items-center space-x-2 px-6 py-3 rounded-xl font-medium transition-all duration-200 ${
              currentStep === 1
                ? 'bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed'
                : 'text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600'
            }`}
          >
            <ArrowLeftIcon className='w-4 h-4' />
            <span>{t('previous')}</span>
          </button>

          {currentStep < totalSteps ? (
            <button
              onClick={nextStep}
              className='inline-flex items-center space-x-2 px-6 py-3 bg-blue-500 text-white hover:bg-blue-600 rounded-xl font-medium transition-all duration-200 shadow-sm hover:shadow-md'
            >
              <span>{t('continue')}</span>
              <ArrowRightIcon className='w-4 h-4' />
            </button>
          ) : (
            <>
              {/* Submit Button */}
              <button
                type='button'
                onClick={handleSubmit}
                disabled={loading}
                className='w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2'
              >
                {loading ? (
                  <>
                    <div className='animate-spin rounded-full h-5 w-5 border-b-2 border-white'></div>
                    <span>{currentStep === 6 ? t('savingProfile') : t('creatingAccount')}</span>
                  </>
                ) : (
                  <>
                    <span>{currentStep === 6 ? t('completeSetupProfile') : t('createAccount')}</span>
                    <ArrowRightIcon className='h-5 w-5' />
                  </>
                )}
              </button>

              {/* Error Message */}
              {errors.submit && (
                <div className='bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3'>
                  <p className='text-sm text-red-600 dark:text-red-400 text-center'>
                    {errors.submit}
                  </p>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </ApplePageWrapper>
  );
};

export default ProfileSetupPage;
