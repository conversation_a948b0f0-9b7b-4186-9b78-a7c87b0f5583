{"name": "vwork", "version": "1.0.0", "description": "VWork - Modern Freelancer Marketplace Platform - Connecting freelancers and clients with advanced features", "private": true, "scripts": {"build": "cd client && npm ci && npm run build", "build:frontend": "cd client && npm ci && npm run build", "build:production": "chmod +x build-production.sh && ./build-production.sh", "build:production:windows": "build-production.bat", "start": "npx kill-port 3000 8080 3001 3002 3003 3004 3005 && concurrently \"npm run start:gateway\" \"npm run start:client\" \"npm run start:auth\" \"npm run start:user\" \"npm run start:project\" \"npm run start:payment\" \"npm run start:chat\"", "start:gateway": "cd services/api-gateway && npm start", "start:client": "cd client && npm start", "start:auth": "cd services/auth-service && set PORT=3001 && npm start", "start:user": "cd services/user-service && set PORT=3002 && npm start", "start:project": "cd services/project-service && set PORT=3003 && npm start", "start:payment": "cd services/payment-service && set PORT=3004 && npm start", "start:chat": "cd services/chat-service && set PORT=3005 && npm start", "start:backend": "node scripts/start-with-backend.js", "dev": "concurrently \"npm run dev:gateway\" \"npm run dev:client\"", "dev:gateway": "cd services/api-gateway && npm run dev", "dev:client": "cd client && npm start", "stop": "node scripts/stop.js", "install:all": "npm install && cd client && npm install && cd ../server && npm install", "setup": "node scripts/setup-all.js", "setup:db": "cd server && npm run setup:db", "setup:manual": "cd server && npm run setup:manual", "db:seed": "cd server && npm run db:seed", "db:reset": "cd server && npm run db:reset", "test": "cd server && npm test && cd ../client && npm test", "build:server": "cd server && npm run build", "clean": "rm -rf node_modules && cd client && rm -rf node_modules && cd ../server && rm -rf node_modules", "clean:install": "npm run clean && npm run install:all", "reset:all": "npm run db:reset && npm run db:seed", "logs": "concurrently \"cd server && npm run dev\" \"cd client && npm start\"", "status": "echo 'Checking VWork system status...' && cd server && npm run dev --silent & cd client && npm start --silent & echo 'VWork system is running!'", "deploy:check": "node deploy-render.js", "deploy:render": "echo 'Ready to deploy to Render. Run: git push origin main'"}, "keywords": ["freelancer", "marketplace", "next.js", "tailwind", "gsap", "modern-ui", "2025-design-trends"], "author": "VWork Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/vwork.git"}, "devDependencies": {"@types/node": "^20.0.0", "autoprefixer": "^10.4.21", "concurrently": "^9.2.0", "cross-env": "^7.0.3", "dotenv": "^16.3.1", "kill-port": "^2.0.1", "postcss": "^8.5.6", "tailwindcss": "^4.1.11"}, "dependencies": {"axios": "^1.10.0", "firebase": "^11.10.0"}}