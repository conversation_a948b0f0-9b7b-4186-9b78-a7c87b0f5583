services:
  # API Gateway
  - type: web
    name: vwork-api-gateway
    env: node
    buildCommand: cd services/api-gateway && npm install
    startCommand: cd services/api-gateway && npm start
    healthCheckPath: /health
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 3000
      - key: AUTH_SERVICE_URL
        value: https://vwork-auth-service.onrender.com
      - key: USER_SERVICE_URL
        value: https://vwork-user-service.onrender.com
      - key: PROJECT_SERVICE_URL
        value: https://vwork-project-service.onrender.com
      - key: CHAT_SERVICE_URL
        value: https://vwork-chat-service.onrender.com
      - key: PAYMENT_SERVICE_URL
        value: https://vwork-payment-service.onrender.com
      - key: CORS_ORIGIN
        value: https://vwork-frontend.onrender.com

  # Auth Service
  - type: web
    name: vwork-auth-service
    env: node
    buildCommand: cd services/auth-service && npm install
    startCommand: cd services/auth-service && npm start
    healthCheckPath: /health
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 3001
      - key: JWT_SECRET
        value: your-jwt-secret-here
      - key: FIREBASE_PROJECT_ID
        value: vwork-786c3
      - key: REACT_APP_FIREBASE_API_KEY
        value: AIzaSyBy8ymWrOGYwcjS-Ii4PgyzWLdb-A4U6nw
      - key: REACT_APP_FIREBASE_AUTH_DOMAIN
        value: vwork-786c3.firebaseapp.com
      - key: REACT_APP_FIREBASE_PROJECT_ID
        value: vwork-786c3
      - key: REACT_APP_FIREBASE_STORAGE_BUCKET
        value: vwork-786c3.firebasestorage.app
      - key: REACT_APP_FIREBASE_MESSAGING_SENDER_ID
        value: 1050922072615
      - key: REACT_APP_FIREBASE_APP_ID
        value: 1:1050922072615:web:dfeae89c9ba66c77aeec02

  # User Service
  - type: web
    name: vwork-user-service
    env: node
    buildCommand: cd services/user-service && npm install
    startCommand: cd services/user-service && npm start
    healthCheckPath: /health
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 3002
      - key: FIREBASE_PROJECT_ID
        value: vwork-786c3
      - key: REACT_APP_FIREBASE_API_KEY
        value: AIzaSyBy8ymWrOGYwcjS-Ii4PgyzWLdb-A4U6nw
      - key: REACT_APP_FIREBASE_AUTH_DOMAIN
        value: vwork-786c3.firebaseapp.com
      - key: REACT_APP_FIREBASE_PROJECT_ID
        value: vwork-786c3
      - key: REACT_APP_FIREBASE_STORAGE_BUCKET
        value: vwork-786c3.firebasestorage.app
      - key: REACT_APP_FIREBASE_MESSAGING_SENDER_ID
        value: 1050922072615
      - key: REACT_APP_FIREBASE_APP_ID
        value: 1:1050922072615:web:dfeae89c9ba66c77aeec02

  # Project Service
  - type: web
    name: vwork-project-service
    env: node
    buildCommand: cd services/project-service && npm install
    startCommand: cd services/project-service && npm start
    healthCheckPath: /health
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 3003
      - key: FIREBASE_PROJECT_ID
        value: vwork-786c3
      - key: REACT_APP_FIREBASE_API_KEY
        value: AIzaSyBy8ymWrOGYwcjS-Ii4PgyzWLdb-A4U6nw
      - key: REACT_APP_FIREBASE_AUTH_DOMAIN
        value: vwork-786c3.firebaseapp.com
      - key: REACT_APP_FIREBASE_PROJECT_ID
        value: vwork-786c3
      - key: REACT_APP_FIREBASE_STORAGE_BUCKET
        value: vwork-786c3.firebasestorage.app
      - key: REACT_APP_FIREBASE_MESSAGING_SENDER_ID
        value: 1050922072615
      - key: REACT_APP_FIREBASE_APP_ID
        value: 1:1050922072615:web:dfeae89c9ba66c77aeec02

  # Chat Service
  - type: web
    name: vwork-chat-service
    env: node
    buildCommand: cd services/chat-service && npm install
    startCommand: cd services/chat-service && npm start
    healthCheckPath: /health
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 3004
      - key: FIREBASE_PROJECT_ID
        value: vwork-786c3
      - key: REACT_APP_FIREBASE_API_KEY
        value: AIzaSyBy8ymWrOGYwcjS-Ii4PgyzWLdb-A4U6nw
      - key: REACT_APP_FIREBASE_AUTH_DOMAIN
        value: vwork-786c3.firebaseapp.com
      - key: REACT_APP_FIREBASE_PROJECT_ID
        value: vwork-786c3
      - key: REACT_APP_FIREBASE_STORAGE_BUCKET
        value: vwork-786c3.firebasestorage.app
      - key: REACT_APP_FIREBASE_MESSAGING_SENDER_ID
        value: 1050922072615
      - key: REACT_APP_FIREBASE_APP_ID
        value: 1:1050922072615:web:dfeae89c9ba66c77aeec02

  # Payment Service
  - type: web
    name: vwork-payment-service
    env: node
    buildCommand: cd services/payment-service && npm install
    startCommand: cd services/payment-service && npm start
    healthCheckPath: /health
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 3005
      - key: FIREBASE_PROJECT_ID
        value: vwork-786c3
      - key: REACT_APP_FIREBASE_API_KEY
        value: AIzaSyBy8ymWrOGYwcjS-Ii4PgyzWLdb-A4U6nw
      - key: REACT_APP_FIREBASE_AUTH_DOMAIN
        value: vwork-786c3.firebaseapp.com
      - key: REACT_APP_FIREBASE_PROJECT_ID
        value: vwork-786c3
      - key: REACT_APP_FIREBASE_STORAGE_BUCKET
        value: vwork-786c3.firebasestorage.app
      - key: REACT_APP_FIREBASE_MESSAGING_SENDER_ID
        value: 1050922072615
      - key: REACT_APP_FIREBASE_APP_ID
        value: 1:1050922072615:web:dfeae89c9ba66c77aeec02

  # Frontend
  - type: web
    name: vwork-frontend
    env: static
    buildCommand: cd client && npm install && npm run build
    staticPublishPath: ./client/build
    routes:
      - type: rewrite
        source: /*
        destination: /index.html
    envVars:
      - key: NODE_ENV
        value: production
      - key: CI
        value: false
      - key: GENERATE_SOURCEMAP
        value: false
      - key: ESLINT_NO_DEV_ERRORS
        value: true
      - key: TSC_COMPILE_ON_ERROR
        value: true
      - key: REACT_APP_API_URL
        value: https://vwork-api-gateway.onrender.com
      - key: REACT_APP_FIREBASE_API_KEY
        value: AIzaSyBy8ymWrOGYwcjS-Ii4PgyzWLdb-A4U6nw
      - key: REACT_APP_FIREBASE_AUTH_DOMAIN
        value: vwork-786c3.firebaseapp.com
      - key: REACT_APP_FIREBASE_PROJECT_ID
        value: vwork-786c3
      - key: REACT_APP_FIREBASE_STORAGE_BUCKET
        value: vwork-786c3.firebasestorage.app
      - key: REACT_APP_FIREBASE_MESSAGING_SENDER_ID
        value: 1050922072615
      - key: REACT_APP_FIREBASE_APP_ID
        value: 1:1050922072615:web:dfeae89c9ba66c77aeec02
