# VWork Platform Backend Services

## Overview
VWork is a microservices-based freelancing platform with the following services:

- **API Gateway** (Port 8080) - Routes requests to appropriate services
- **Auth Service** (Port 3001) - Firebase authentication and user management
- **User Service** (Port 3002) - User profiles and freelancer management
- **Project Service** (Port 3003) - Project creation, bidding, and management
- **Job Service** (Port 3004) - Job postings and applications
- **Chat Service** (Port 3005) - Real-time messaging
- **Search Service** (Port 3009) - Global search across all content

## Quick Start

### Prerequisites
- Node.js 16+ 
- npm or yarn
- Firebase project setup

### Environment Variables
Create `.env` files in each service directory with:

```bash
# Firebase Configuration
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_SERVICE_ACCOUNT_KEY=your-service-account-json

# Service URLs (for production)
AUTH_SERVICE_URL=http://localhost:3001
USER_SERVICE_URL=http://localhost:3002
PROJECT_SERVICE_URL=http://localhost:3003
JOB_SERVICE_URL=http://localhost:3004
CHAT_SERVICE_URL=http://localhost:3005
SEARCH_SERVICE_URL=http://localhost:3009

# CORS Origins
CORS_ORIGIN=http://localhost:3000,http://localhost:3006
```

### Installation

1. **Install dependencies for all services:**
```bash
# Install shared utilities
cd shared && npm install

# Install each service
cd ../auth-service && npm install
cd ../user-service && npm install  
cd ../project-service && npm install
cd ../job-service && npm install
cd ../chat-service && npm install
cd ../search-service && npm install
cd ../api-gateway && npm install
```

2. **Start all services:**
```bash
# From services directory
node start-all.js
```

Or start individually:
```bash
# API Gateway
cd api-gateway && npm run dev

# Auth Service  
cd auth-service && npm run dev

# User Service
cd user-service && npm run dev

# Project Service
cd project-service && npm run dev

# Job Service
cd job-service && npm run dev

# Chat Service
cd chat-service && npm run dev

# Search Service
cd search-service && npm run dev
```

### Testing

Run API integration tests:
```bash
# Install axios for testing
npm install axios

# Run tests
node test-api.js
```

## API Documentation

### Base URL
- Development: `http://localhost:8080/api/v1`
- All endpoints return standardized JSON responses

### Authentication
Protected endpoints require Firebase ID token:
```
Authorization: Bearer <firebase_id_token>
```

### Key Endpoints

#### Authentication
- `POST /auth/register` - Register new user
- `POST /auth/login` - Login with Firebase token
- `GET /auth/me` - Get current user info

#### Users & Freelancers
- `GET /users/:id` - Get user profile
- `PUT /users/:id` - Update user profile
- `GET /freelancers` - List freelancers with filters

#### Projects
- `GET /projects` - List projects with filters
- `GET /projects/:id` - Get project details
- `POST /projects` - Create new project
- `POST /projects/:id/bids` - Submit bid

#### Jobs
- `GET /jobs` - List jobs with filters
- `GET /jobs/:id` - Get job details
- `POST /jobs` - Create new job
- `POST /jobs/:id/apply` - Apply for job

#### Search
- `GET /search` - Global search
- `GET /search/suggestions` - Search suggestions
- `GET /search/popular` - Popular searches

### Response Format
```json
{
  "success": true,
  "data": {},
  "message": "Success message",
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "totalPages": 5
  }
}
```

## Architecture

### Microservices Pattern
- Each service is independent and can be deployed separately
- Services communicate via HTTP APIs
- API Gateway handles routing and load balancing
- Shared utilities for common functionality

### Technology Stack
- **Runtime:** Node.js + Express
- **Authentication:** Firebase Auth
- **Validation:** Joi
- **Security:** Helmet, CORS
- **Logging:** Morgan
- **Process Management:** PM2 (production)

### Shared Components
- Response utilities (`shared/utils/response.js`)
- Authentication middleware (`shared/middleware/auth.js`)
- Validation schemas (`shared/middleware/validation.js`)

## Development

### Adding New Endpoints
1. Add route to appropriate service
2. Update validation schemas if needed
3. Add proxy route in API Gateway
4. Update API documentation
5. Add tests

### Service Communication
Services can call each other via HTTP:
```javascript
const axios = require('axios');
const response = await axios.get('http://localhost:3002/users/123');
```

### Error Handling
All services use standardized error responses:
```javascript
res.apiError('Error message', 'ERROR_CODE', 500);
```

## Production Deployment

### Docker Support
Each service includes Dockerfile for containerization.

### Environment Configuration
- Use environment variables for all configuration
- Separate configs for dev/staging/production
- Use secrets management for sensitive data

### Monitoring
- Health check endpoints: `/health`
- Structured logging with request IDs
- Performance metrics collection

## Troubleshooting

### Common Issues

1. **Port conflicts:** Check if ports 3001-3005, 3009, 8080 are available
2. **Firebase errors:** Verify Firebase configuration and service account
3. **CORS issues:** Update CORS_ORIGIN environment variable
4. **Service unavailable:** Check if all services are running

### Health Checks
- API Gateway: `http://localhost:8080/health`
- Auth Service: `http://localhost:3001/health`
- User Service: `http://localhost:3002/health`
- Project Service: `http://localhost:3003/health`
- Job Service: `http://localhost:3004/health`
- Chat Service: `http://localhost:3005/health`
- Search Service: `http://localhost:3009/health`

### Logs
Each service logs to console with structured format:
```
[Service Name] LOG_LEVEL: Message
```

## Contributing

1. Follow existing code structure and patterns
2. Add validation for all inputs
3. Include error handling
4. Update documentation
5. Add tests for new features

## License
MIT License
