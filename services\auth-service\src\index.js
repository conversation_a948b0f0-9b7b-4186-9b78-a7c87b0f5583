const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(helmet());
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:8080'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'x-auth-token']
}));
app.use(morgan('combined'));
app.use(express.json());

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    service: 'Auth Service',
    timestamp: new Date().toISOString()
  });
});

// Auth routes
app.post('/auth/login', (req, res) => {
  console.log('🔐 Login request:', req.body);
  res.json({
    success: true,
    message: 'Login successful',
    user: {
      id: 'user-123',
      email: req.body.email,
      name: 'Test User',
      role: 'freelancer'
    },
    token: 'mock-jwt-token'
  });
});

app.get('/auth/me', (req, res) => {
  res.json({
    success: true,
    user: {
      id: 'user-123',
      email: '<EMAIL>',
      name: 'Test User',
      role: 'freelancer'
    }
  });
});

app.listen(PORT, () => {
  console.log(`🔐 Auth Service running on port ${PORT}`);
}); 