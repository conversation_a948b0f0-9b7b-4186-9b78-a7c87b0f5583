const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
require('dotenv').config();

// Import shared utilities
const { responseMiddleware } = require('../../shared/utils/response');
const { verifyFirebaseToken, admin } = require('../../shared/middleware/auth');
const { validateBody, schemas } = require('../../shared/middleware/validation');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(helmet());
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3006', 'http://localhost:8080'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'x-auth-token']
}));
app.use(morgan('combined'));
app.use(express.json());
app.use(responseMiddleware);

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    service: 'Auth Service',
    timestamp: new Date().toISOString(),
    port: PORT
  });
});

// Auth routes
app.post('/auth/register', validateBody(schemas.userRegistration), async (req, res) => {
  try {
    const { firebaseToken, userType, name, guildSpecialization } = req.body;

    console.log('🔐 Registration request:', { userType, name });

    // Verify Firebase token
    const decodedToken = await admin.auth().verifyIdToken(firebaseToken);

    // Create user data
    const userData = {
      uid: decodedToken.uid,
      email: decodedToken.email,
      name: name || decodedToken.name,
      userType,
      guildSpecialization: guildSpecialization || '',
      emailVerified: decodedToken.email_verified,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      profile: {
        bio: '',
        avatar: decodedToken.picture || null,
        location: {
          country: '',
          city: '',
          timezone: ''
        },
        website: '',
        phoneNumber: '',
        skills: [],
        hourlyRate: userType === 'freelancer' ? 0 : null,
        availability: userType === 'freelancer' ? 'available' : null,
        isComplete: false
      }
    };

    // TODO: Save to database
    console.log('✅ User registered:', userData);

    res.apiSuccess(userData, 'Registration successful');

  } catch (error) {
    console.error('❌ Registration failed:', error);

    if (error.code && error.code.startsWith('auth/')) {
      res.apiUnauthorized('Invalid Firebase token');
    } else {
      res.apiError('Registration failed', 'REGISTRATION_ERROR', 500);
    }
  }
});

app.post('/auth/login', validateBody(schemas.userLogin), async (req, res) => {
  try {
    const { firebaseToken } = req.body;

    console.log('🔐 Login request');

    // Verify Firebase token
    const decodedToken = await admin.auth().verifyIdToken(firebaseToken);

    // TODO: Get user from database
    const userData = {
      uid: decodedToken.uid,
      email: decodedToken.email,
      name: decodedToken.name,
      emailVerified: decodedToken.email_verified,
      lastLoginAt: new Date().toISOString()
    };

    console.log('✅ Login successful:', userData.email);

    res.apiSuccess(userData, 'Login successful');

  } catch (error) {
    console.error('❌ Login failed:', error);

    if (error.code && error.code.startsWith('auth/')) {
      res.apiUnauthorized('Invalid Firebase token');
    } else {
      res.apiError('Login failed', 'LOGIN_ERROR', 500);
    }
  }
});

app.get('/auth/me', verifyFirebaseToken, async (req, res) => {
  try {
    // TODO: Get full user data from database
    const userData = {
      uid: req.user.uid,
      email: req.user.email,
      name: req.user.name,
      emailVerified: req.user.emailVerified,
      picture: req.user.picture
    };

    res.apiSuccess(userData, 'User data retrieved');

  } catch (error) {
    console.error('❌ Get user failed:', error);
    res.apiError('Failed to get user data', 'GET_USER_ERROR', 500);
  }
});

app.post('/auth/logout', verifyFirebaseToken, async (req, res) => {
  try {
    // TODO: Invalidate any backend tokens/sessions
    console.log('🔐 Logout:', req.user.email);

    res.apiSuccess(null, 'Logout successful');

  } catch (error) {
    console.error('❌ Logout failed:', error);
    res.apiError('Logout failed', 'LOGOUT_ERROR', 500);
  }
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('🚨 Auth Service Error:', error);
  res.apiError('Internal server error', 'INTERNAL_ERROR', 500);
});

app.listen(PORT, () => {
  console.log(`🔐 Auth Service running on port ${PORT}`);
  console.log(`🏥 Health check: http://localhost:${PORT}/health`);
});