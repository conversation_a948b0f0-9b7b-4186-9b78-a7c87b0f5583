const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
require('dotenv').config();

// Import shared utilities
const { responseMiddleware, createPagination } = require('../../shared/utils/response');
const { verifyFirebaseToken, optionalAuth } = require('../../shared/middleware/auth');
const { validateBody, validateQuery, schemas } = require('../../shared/middleware/validation');

const app = express();
const PORT = process.env.PORT || 3004;

// Middleware
app.use(helmet());
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3006', 'http://localhost:8080'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'x-auth-token']
}));
app.use(morgan('combined'));
app.use(express.json());
app.use(responseMiddleware);

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    service: 'Job Service',
    port: PORT,
    timestamp: new Date().toISOString()
  });
});

// Mock data
const mockJobs = [
  {
    id: 'job-1',
    title: 'Senior React Developer',
    description: 'We are looking for an experienced React developer to join our team...',
    company: 'TechCorp Vietnam',
    location: 'Ho Chi Minh City, Vietnam',
    type: 'full-time',
    category: 'Software Development',
    salary: {
      min: 2000,
      max: 3500,
      currency: 'USD',
      period: 'month'
    },
    skills: ['React', 'JavaScript', 'TypeScript', 'Node.js'],
    requirements: [
      '3+ years of React experience',
      'Strong JavaScript/TypeScript skills',
      'Experience with modern frontend tools'
    ],
    benefits: [
      'Competitive salary',
      'Health insurance',
      'Flexible working hours',
      'Remote work options'
    ],
    postedBy: 'client-1',
    postedAt: '2024-01-15T10:00:00.000Z',
    deadline: '2024-02-15T23:59:59.000Z',
    status: 'active',
    applicationsCount: 12
  },
  {
    id: 'job-2',
    title: 'UI/UX Designer',
    description: 'Join our design team to create amazing user experiences...',
    company: 'Design Studio',
    location: 'Hanoi, Vietnam',
    type: 'part-time',
    category: 'Design',
    salary: {
      min: 15,
      max: 25,
      currency: 'USD',
      period: 'hour'
    },
    skills: ['Figma', 'Adobe XD', 'Sketch', 'Prototyping'],
    requirements: [
      '2+ years of UI/UX design experience',
      'Proficiency in design tools',
      'Portfolio required'
    ],
    benefits: [
      'Flexible schedule',
      'Creative environment',
      'Learning opportunities'
    ],
    postedBy: 'client-2',
    postedAt: '2024-01-10T14:30:00.000Z',
    deadline: '2024-02-10T23:59:59.000Z',
    status: 'active',
    applicationsCount: 8
  }
];

// Job routes
app.get('/jobs', validateQuery(schemas.pagination), async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      category, 
      type, 
      location, 
      skills,
      salaryMin,
      salaryMax 
    } = req.query;
    
    console.log('💼 Get jobs:', { page, limit, category, type, location, skills });
    
    // TODO: Apply filters and get from database
    let filteredJobs = [...mockJobs];
    
    if (category) {
      filteredJobs = filteredJobs.filter(job => 
        job.category.toLowerCase().includes(category.toLowerCase())
      );
    }
    
    if (type) {
      filteredJobs = filteredJobs.filter(job => job.type === type);
    }
    
    if (location) {
      filteredJobs = filteredJobs.filter(job => 
        job.location.toLowerCase().includes(location.toLowerCase())
      );
    }
    
    if (skills) {
      const skillsArray = Array.isArray(skills) ? skills : [skills];
      filteredJobs = filteredJobs.filter(job => 
        skillsArray.some(skill => 
          job.skills.some(jobSkill => 
            jobSkill.toLowerCase().includes(skill.toLowerCase())
          )
        )
      );
    }
    
    const total = filteredJobs.length;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedJobs = filteredJobs.slice(startIndex, endIndex);
    
    const pagination = createPagination(page, limit, total);
    
    res.apiSuccess(paginatedJobs, 'Jobs retrieved successfully', pagination);
    
  } catch (error) {
    console.error('❌ Get jobs failed:', error);
    res.apiError('Failed to get jobs', 'GET_JOBS_ERROR', 500);
  }
});

app.get('/jobs/:id', optionalAuth, async (req, res) => {
  try {
    const { id } = req.params;
    
    console.log(`💼 Get job details: ${id}`);
    
    // TODO: Get job from database
    const job = mockJobs.find(j => j.id === id);
    
    if (!job) {
      return res.apiNotFound('Job not found');
    }
    
    res.apiSuccess(job, 'Job details retrieved');
    
  } catch (error) {
    console.error('❌ Get job failed:', error);
    res.apiError('Failed to get job details', 'GET_JOB_ERROR', 500);
  }
});

app.post('/jobs', verifyFirebaseToken, validateBody(schemas.jobCreate), async (req, res) => {
  try {
    const jobData = req.body;
    
    console.log('💼 Create job:', jobData.title);
    
    // TODO: Save job to database
    const newJob = {
      id: `job-${Date.now()}`,
      ...jobData,
      postedBy: req.user.uid,
      postedAt: new Date().toISOString(),
      status: 'active',
      applicationsCount: 0
    };
    
    console.log('✅ Job created:', newJob.id);
    
    res.apiSuccess(newJob, 'Job created successfully');
    
  } catch (error) {
    console.error('❌ Create job failed:', error);
    res.apiError('Failed to create job', 'CREATE_JOB_ERROR', 500);
  }
});

app.put('/jobs/:id', verifyFirebaseToken, async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    
    console.log(`💼 Update job: ${id}`);
    
    // TODO: Check ownership and update in database
    const updatedJob = {
      id,
      ...updateData,
      updatedAt: new Date().toISOString()
    };
    
    res.apiSuccess(updatedJob, 'Job updated successfully');
    
  } catch (error) {
    console.error('❌ Update job failed:', error);
    res.apiError('Failed to update job', 'UPDATE_JOB_ERROR', 500);
  }
});

app.delete('/jobs/:id', verifyFirebaseToken, async (req, res) => {
  try {
    const { id } = req.params;
    
    console.log(`💼 Delete job: ${id}`);
    
    // TODO: Check ownership and delete from database
    
    res.apiSuccess(null, 'Job deleted successfully');
    
  } catch (error) {
    console.error('❌ Delete job failed:', error);
    res.apiError('Failed to delete job', 'DELETE_JOB_ERROR', 500);
  }
});

app.post('/jobs/:id/apply', verifyFirebaseToken, validateBody(schemas.jobApplication), async (req, res) => {
  try {
    const { id } = req.params;
    const applicationData = req.body;
    
    console.log(`💼 Apply for job: ${id}`);
    
    // TODO: Save application to database
    const application = {
      id: `app-${Date.now()}`,
      jobId: id,
      applicantId: req.user.uid,
      ...applicationData,
      appliedAt: new Date().toISOString(),
      status: 'pending'
    };
    
    console.log('✅ Application submitted:', application.id);
    
    res.apiSuccess(application, 'Application submitted successfully');
    
  } catch (error) {
    console.error('❌ Job application failed:', error);
    res.apiError('Failed to submit application', 'APPLY_JOB_ERROR', 500);
  }
});

app.get('/jobs/:id/applications', verifyFirebaseToken, async (req, res) => {
  try {
    const { id } = req.params;
    
    console.log(`💼 Get job applications: ${id}`);
    
    // TODO: Check ownership and get applications from database
    const mockApplications = [
      {
        id: 'app-1',
        jobId: id,
        applicantId: 'freelancer-1',
        applicantName: 'Alice Johnson',
        coverLetter: 'I am very interested in this position...',
        resume: 'https://example.com/resume.pdf',
        appliedAt: '2024-01-16T10:00:00.000Z',
        status: 'pending'
      }
    ];
    
    res.apiSuccess(mockApplications, 'Job applications retrieved');
    
  } catch (error) {
    console.error('❌ Get applications failed:', error);
    res.apiError('Failed to get applications', 'GET_APPLICATIONS_ERROR', 500);
  }
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('🚨 Job Service Error:', error);
  res.apiError('Internal server error', 'INTERNAL_ERROR', 500);
});

app.listen(PORT, () => {
  console.log(`💼 Job Service running on port ${PORT}`);
  console.log(`🏥 Health check: http://localhost:${PORT}/health`);
});

module.exports = app;
