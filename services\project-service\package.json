{"name": "project-service", "version": "1.0.0", "description": "project-service for VWork platform", "main": "src/index.js", "scripts": {"start": "node src/index.js", "build": "echo 'No build needed for Node.js backend'", "dev": "nodemon src/index.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "helmet": "^7.1.0", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0"}}