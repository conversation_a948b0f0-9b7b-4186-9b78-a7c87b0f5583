const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3003;

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json());

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    service: 'Project Service',
    port: PORT,
    timestamp: new Date().toISOString()
  });
});

// Project routes
app.get('/projects', (req, res) => {
  console.log('📋 Get projects request');
  res.json({
    success: true,
    message: 'Projects retrieved successfully',
    data: []
  });
});

app.post('/projects', (req, res) => {
  console.log('📄 Create project request:', req.body);
  res.json({
    success: true,
    message: 'Project created successfully',
    data: { id: Date.now(), ...req.body }
  });
});

app.listen(PORT, () => {
  console.log(`🚀 Project Service running on port ${PORT}`);
  console.log(`🏥 Health check: http://localhost:${PORT}/health`);
});

module.exports = app;
