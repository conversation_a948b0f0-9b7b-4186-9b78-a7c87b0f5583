const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const axios = require('axios');
require('dotenv').config();

// Import shared utilities
const { responseMiddleware, createPagination } = require('../../shared/utils/response');
const { optionalAuth } = require('../../shared/middleware/auth');
const { validateQuery, schemas } = require('../../shared/middleware/validation');

const app = express();
const PORT = process.env.PORT || 3009;

// Service URLs
const SERVICES = {
  projects: process.env.PROJECT_SERVICE_URL || 'http://localhost:3003',
  jobs: process.env.JOB_SERVICE_URL || 'http://localhost:3004',
  users: process.env.USER_SERVICE_URL || 'http://localhost:3002'
};

// Middleware
app.use(helmet());
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3006', 'http://localhost:8080'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'x-auth-token']
}));
app.use(morgan('combined'));
app.use(express.json());
app.use(responseMiddleware);

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    service: 'Search Service',
    port: PORT,
    timestamp: new Date().toISOString()
  });
});

// Helper function to search in a service
const searchInService = async (serviceUrl, endpoint, query, filters = {}) => {
  try {
    const params = new URLSearchParams({
      ...filters,
      q: query,
      limit: 50 // Get more results for search
    });

    const response = await axios.get(`${serviceUrl}${endpoint}?${params}`, {
      timeout: 5000
    });

    return response.data.success ? response.data.data : [];
  } catch (error) {
    console.error(`Search error in ${serviceUrl}${endpoint}:`, error.message);
    return [];
  }
};

// Helper function to filter results by search query
const filterByQuery = (items, query, searchFields) => {
  if (!query) return items;
  
  const searchTerm = query.toLowerCase();
  
  return items.filter(item => {
    return searchFields.some(field => {
      const value = getNestedValue(item, field);
      if (Array.isArray(value)) {
        return value.some(v => v.toString().toLowerCase().includes(searchTerm));
      }
      return value && value.toString().toLowerCase().includes(searchTerm);
    });
  });
};

// Helper function to get nested object values
const getNestedValue = (obj, path) => {
  return path.split('.').reduce((current, key) => current && current[key], obj);
};

// Global search endpoint
app.get('/search', optionalAuth, validateQuery(schemas.search), async (req, res) => {
  try {
    const { q, type = 'all', page = 1, limit = 20, ...filters } = req.query;
    
    console.log('🔍 Global search:', { q, type, page, limit, filters });
    
    let results = [];
    
    if (type === 'all' || type === 'projects') {
      // Search projects
      const projects = await searchInService(SERVICES.projects, '/projects', q, {
        category: filters.category,
        skills: filters.skills
      });
      
      const filteredProjects = filterByQuery(projects, q, [
        'title', 'description', 'category', 'skills', 'clientName'
      ]);
      
      results.push(...filteredProjects.map(project => ({
        ...project,
        type: 'project',
        relevance: calculateRelevance(project, q, ['title', 'description'])
      })));
    }
    
    if (type === 'all' || type === 'jobs') {
      // Search jobs
      const jobs = await searchInService(SERVICES.jobs, '/jobs', q, {
        category: filters.category,
        type: filters.jobType,
        location: filters.location
      });
      
      const filteredJobs = filterByQuery(jobs, q, [
        'title', 'description', 'company', 'location', 'skills'
      ]);
      
      results.push(...filteredJobs.map(job => ({
        ...job,
        type: 'job',
        relevance: calculateRelevance(job, q, ['title', 'description'])
      })));
    }
    
    if (type === 'all' || type === 'freelancers') {
      // Search freelancers
      const freelancers = await searchInService(SERVICES.users, '/freelancers', q, {
        skills: filters.skills,
        location: filters.location
      });
      
      const filteredFreelancers = filterByQuery(freelancers, q, [
        'name', 'profile.bio', 'profile.skills', 'profile.location.city'
      ]);
      
      results.push(...filteredFreelancers.map(freelancer => ({
        ...freelancer,
        type: 'freelancer',
        relevance: calculateRelevance(freelancer, q, ['name', 'profile.bio'])
      })));
    }
    
    // Sort by relevance
    results.sort((a, b) => b.relevance - a.relevance);
    
    // Apply pagination
    const total = results.length;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedResults = results.slice(startIndex, endIndex);
    
    const pagination = createPagination(page, limit, total);
    
    // Group results by type for better presentation
    const groupedResults = {
      all: paginatedResults,
      projects: paginatedResults.filter(r => r.type === 'project'),
      jobs: paginatedResults.filter(r => r.type === 'job'),
      freelancers: paginatedResults.filter(r => r.type === 'freelancer')
    };
    
    res.apiSuccess({
      query: q,
      type,
      results: type === 'all' ? groupedResults : paginatedResults,
      summary: {
        total,
        projects: results.filter(r => r.type === 'project').length,
        jobs: results.filter(r => r.type === 'job').length,
        freelancers: results.filter(r => r.type === 'freelancer').length
      }
    }, 'Search completed successfully', pagination);
    
  } catch (error) {
    console.error('❌ Search failed:', error);
    res.apiError('Search failed', 'SEARCH_ERROR', 500);
  }
});

// Calculate relevance score for search results
const calculateRelevance = (item, query, searchFields) => {
  if (!query) return 0;
  
  const searchTerm = query.toLowerCase();
  let score = 0;
  
  searchFields.forEach((field, index) => {
    const value = getNestedValue(item, field);
    if (value) {
      const text = value.toString().toLowerCase();
      
      // Exact match in title gets highest score
      if (field.includes('title') && text.includes(searchTerm)) {
        score += 10;
      }
      // Partial match in title
      else if (field.includes('title') && text.split(' ').some(word => word.includes(searchTerm))) {
        score += 5;
      }
      // Match in description or other fields
      else if (text.includes(searchTerm)) {
        score += 2;
      }
      // Partial word match
      else if (text.split(' ').some(word => word.includes(searchTerm))) {
        score += 1;
      }
    }
  });
  
  return score;
};

// Autocomplete/suggestions endpoint
app.get('/search/suggestions', optionalAuth, async (req, res) => {
  try {
    const { q, type = 'all', limit = 10 } = req.query;
    
    if (!q || q.length < 2) {
      return res.apiSuccess([], 'Query too short for suggestions');
    }
    
    console.log('💡 Search suggestions:', { q, type, limit });
    
    // Mock suggestions - in real implementation, this would come from a search index
    const suggestions = [
      'React Developer',
      'UI/UX Designer', 
      'Node.js Backend',
      'Mobile App Development',
      'E-commerce Website',
      'Logo Design',
      'Content Writing',
      'Digital Marketing'
    ].filter(suggestion => 
      suggestion.toLowerCase().includes(q.toLowerCase())
    ).slice(0, parseInt(limit));
    
    res.apiSuccess(suggestions, 'Suggestions retrieved');
    
  } catch (error) {
    console.error('❌ Suggestions failed:', error);
    res.apiError('Failed to get suggestions', 'SUGGESTIONS_ERROR', 500);
  }
});

// Popular searches endpoint
app.get('/search/popular', optionalAuth, async (req, res) => {
  try {
    const { type = 'all', limit = 10 } = req.query;
    
    console.log('🔥 Popular searches:', { type, limit });
    
    // Mock popular searches - in real implementation, this would come from analytics
    const popularSearches = [
      { query: 'React Developer', count: 150, type: 'job' },
      { query: 'Logo Design', count: 120, type: 'project' },
      { query: 'Mobile App', count: 100, type: 'project' },
      { query: 'UI Designer', count: 90, type: 'freelancer' },
      { query: 'WordPress', count: 80, type: 'project' },
      { query: 'Content Writer', count: 70, type: 'freelancer' },
      { query: 'E-commerce', count: 60, type: 'project' },
      { query: 'Python Developer', count: 50, type: 'job' }
    ];
    
    let filtered = popularSearches;
    if (type !== 'all') {
      filtered = popularSearches.filter(search => search.type === type);
    }
    
    const results = filtered.slice(0, parseInt(limit));
    
    res.apiSuccess(results, 'Popular searches retrieved');
    
  } catch (error) {
    console.error('❌ Popular searches failed:', error);
    res.apiError('Failed to get popular searches', 'POPULAR_SEARCHES_ERROR', 500);
  }
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('🚨 Search Service Error:', error);
  res.apiError('Internal server error', 'INTERNAL_ERROR', 500);
});

app.listen(PORT, () => {
  console.log(`🔍 Search Service running on port ${PORT}`);
  console.log(`🏥 Health check: http://localhost:${PORT}/health`);
});

module.exports = app;
