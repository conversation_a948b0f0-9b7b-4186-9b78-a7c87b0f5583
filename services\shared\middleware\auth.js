/**
 * Firebase Authentication Middleware
 */
const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
if (!admin.apps.length) {
  try {
    // In production, use service account key
    if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
      const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY);
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: process.env.FIREBASE_PROJECT_ID
      });
    } else {
      // In development, use default credentials
      admin.initializeApp({
        projectId: process.env.FIREBASE_PROJECT_ID || 'vwork-platform'
      });
    }
    console.log('✅ Firebase Admin initialized');
  } catch (error) {
    console.error('❌ Firebase Admin initialization failed:', error.message);
  }
}

/**
 * Middleware to verify Firebase ID token
 */
const verifyFirebaseToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.apiUnauthorized('No valid authorization token provided');
    }

    const idToken = authHeader.split('Bearer ')[1];
    
    if (!idToken) {
      return res.apiUnauthorized('No token provided');
    }

    // Verify the Firebase ID token
    const decodedToken = await admin.auth().verifyIdToken(idToken);
    
    // Add user info to request
    req.user = {
      uid: decodedToken.uid,
      email: decodedToken.email,
      emailVerified: decodedToken.email_verified,
      name: decodedToken.name,
      picture: decodedToken.picture,
      firebaseUser: decodedToken
    };

    console.log(`🔐 Authenticated user: ${req.user.email} (${req.user.uid})`);
    next();
    
  } catch (error) {
    console.error('🚨 Token verification failed:', error.message);
    
    if (error.code === 'auth/id-token-expired') {
      return res.apiUnauthorized('Token has expired');
    } else if (error.code === 'auth/id-token-revoked') {
      return res.apiUnauthorized('Token has been revoked');
    } else if (error.code === 'auth/invalid-id-token') {
      return res.apiUnauthorized('Invalid token format');
    } else {
      return res.apiUnauthorized('Token verification failed');
    }
  }
};

/**
 * Optional authentication middleware
 * Adds user info if token is present but doesn't require it
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next();
    }

    const idToken = authHeader.split('Bearer ')[1];
    
    if (!idToken) {
      return next();
    }

    const decodedToken = await admin.auth().verifyIdToken(idToken);
    
    req.user = {
      uid: decodedToken.uid,
      email: decodedToken.email,
      emailVerified: decodedToken.email_verified,
      name: decodedToken.name,
      picture: decodedToken.picture,
      firebaseUser: decodedToken
    };

    console.log(`🔐 Optional auth - user: ${req.user.email}`);
    
  } catch (error) {
    console.log('ℹ️ Optional auth failed, continuing without user');
  }
  
  next();
};

/**
 * Middleware to check if user has specific role
 */
const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.apiUnauthorized('Authentication required');
    }

    if (!req.user.role || !roles.includes(req.user.role)) {
      return res.apiForbidden('Insufficient permissions');
    }

    next();
  };
};

/**
 * Middleware to check if user owns the resource
 */
const requireOwnership = (userIdField = 'userId') => {
  return (req, res, next) => {
    if (!req.user) {
      return res.apiUnauthorized('Authentication required');
    }

    const resourceUserId = req.params[userIdField] || req.body[userIdField];
    
    if (resourceUserId !== req.user.uid) {
      return res.apiForbidden('You can only access your own resources');
    }

    next();
  };
};

module.exports = {
  verifyFirebaseToken,
  optionalAuth,
  requireRole,
  requireOwnership,
  admin
};
