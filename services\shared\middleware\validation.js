/**
 * Validation middleware using Joi
 */
const Joi = require('joi');

/**
 * Generic validation middleware
 */
const validate = (schema, property = 'body') => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req[property], {
      abortEarly: false,
      allowUnknown: false,
      stripUnknown: true
    });

    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context.value
      }));

      return res.apiValidationError(errors, 'Validation failed');
    }

    // Replace the original data with validated data
    req[property] = value;
    next();
  };
};

/**
 * Common validation schemas
 */
const schemas = {
  // Pagination
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20)
  }),

  // User schemas
  userRegistration: Joi.object({
    firebaseToken: Joi.string().required(),
    userType: Joi.string().valid('freelancer', 'client').required(),
    name: Joi.string().min(2).max(100).required(),
    guildSpecialization: Joi.string().max(100).optional()
  }),

  userLogin: Joi.object({
    firebaseToken: Joi.string().required()
  }),

  userProfile: Joi.object({
    profile: Joi.object({
      bio: Joi.string().max(1000).optional(),
      avatar: Joi.string().uri().optional(),
      location: Joi.object({
        country: Joi.string().max(100).optional(),
        city: Joi.string().max(100).optional(),
        timezone: Joi.string().max(50).optional()
      }).optional(),
      website: Joi.string().uri().optional(),
      phoneNumber: Joi.string().max(20).optional(),
      skills: Joi.array().items(Joi.string().max(50)).max(20).optional(),
      hourlyRate: Joi.number().min(0).max(10000).optional(),
      availability: Joi.string().valid('available', 'busy', 'unavailable').optional(),
      isComplete: Joi.boolean().optional()
    }).required()
  }),

  // Project schemas
  projectCreate: Joi.object({
    title: Joi.string().min(5).max(200).required(),
    description: Joi.string().min(20).max(5000).required(),
    category: Joi.string().max(100).required(),
    budget: Joi.object({
      type: Joi.string().valid('fixed', 'hourly').required(),
      amount: Joi.number().min(0).required(),
      currency: Joi.string().length(3).default('USD')
    }).required(),
    skills: Joi.array().items(Joi.string().max(50)).min(1).max(10).required(),
    deadline: Joi.date().iso().min('now').optional(),
    attachments: Joi.array().items(Joi.string().uri()).max(5).optional()
  }),

  // Job schemas
  jobCreate: Joi.object({
    title: Joi.string().min(5).max(200).required(),
    description: Joi.string().min(20).max(5000).required(),
    company: Joi.string().min(2).max(200).required(),
    location: Joi.string().max(200).required(),
    type: Joi.string().valid('full-time', 'part-time', 'contract', 'freelance').required(),
    category: Joi.string().max(100).required(),
    salary: Joi.object({
      min: Joi.number().min(0).required(),
      max: Joi.number().min(Joi.ref('min')).required(),
      currency: Joi.string().length(3).default('USD'),
      period: Joi.string().valid('hour', 'month', 'year').required()
    }).required(),
    skills: Joi.array().items(Joi.string().max(50)).min(1).max(10).required(),
    requirements: Joi.array().items(Joi.string().max(200)).max(10).optional(),
    benefits: Joi.array().items(Joi.string().max(200)).max(10).optional()
  }),

  jobApplication: Joi.object({
    coverLetter: Joi.string().min(50).max(2000).required(),
    resume: Joi.string().uri().required(),
    portfolio: Joi.array().items(Joi.string().uri()).max(5).optional()
  }),

  // Message schemas
  messageCreate: Joi.object({
    content: Joi.string().min(1).max(2000).required(),
    type: Joi.string().valid('text', 'file', 'image').default('text')
  }),

  // Community schemas
  postCreate: Joi.object({
    title: Joi.string().min(5).max(200).required(),
    content: Joi.string().min(20).max(10000).required(),
    category: Joi.string().max(100).required(),
    tags: Joi.array().items(Joi.string().max(50)).max(10).optional(),
    attachments: Joi.array().items(Joi.string().uri()).max(5).optional()
  }),

  // Search schemas
  search: Joi.object({
    q: Joi.string().min(1).max(200).required(),
    type: Joi.string().valid('projects', 'jobs', 'freelancers', 'all').default('all'),
    filters: Joi.object().optional()
  })
};

/**
 * Validation middleware factories
 */
const validateBody = (schema) => validate(schema, 'body');
const validateQuery = (schema) => validate(schema, 'query');
const validateParams = (schema) => validate(schema, 'params');

module.exports = {
  validate,
  validateBody,
  validateQuery,
  validateParams,
  schemas
};
