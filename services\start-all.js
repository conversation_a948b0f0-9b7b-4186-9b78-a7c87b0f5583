#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to start all VWork microservices
 */

const { spawn } = require('child_process');
const path = require('path');

// Service configurations
const services = [
  {
    name: 'API Gateway',
    path: './api-gateway',
    port: 8080,
    command: 'npm',
    args: ['run', 'dev']
  },
  {
    name: 'Auth Service',
    path: './auth-service',
    port: 3001,
    command: 'npm',
    args: ['run', 'dev']
  },
  {
    name: 'User Service',
    path: './user-service',
    port: 3002,
    command: 'npm',
    args: ['run', 'dev']
  },
  {
    name: 'Project Service',
    path: './project-service',
    port: 3003,
    command: 'npm',
    args: ['run', 'dev']
  },
  {
    name: 'Job Service',
    path: './job-service',
    port: 3004,
    command: 'npm',
    args: ['run', 'dev']
  },
  {
    name: 'Chat Service',
    path: './chat-service',
    port: 3005,
    command: 'npm',
    args: ['run', 'dev']
  },
  {
    name: 'Search Service',
    path: './search-service',
    port: 3009,
    command: 'npm',
    args: ['run', 'dev']
  }
];

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Function to log with colors
const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

// Function to start a service
const startService = (service) => {
  return new Promise((resolve, reject) => {
    const servicePath = path.join(__dirname, service.path);
    
    log(`🚀 Starting ${service.name} on port ${service.port}...`, 'cyan');
    
    const child = spawn(service.command, service.args, {
      cwd: servicePath,
      stdio: 'pipe',
      shell: true
    });

    let started = false;

    child.stdout.on('data', (data) => {
      const output = data.toString();
      console.log(`[${service.name}] ${output.trim()}`);
      
      // Check if service has started successfully
      if (output.includes(`running on port ${service.port}`) || 
          output.includes('Server running') ||
          output.includes('started')) {
        if (!started) {
          started = true;
          log(`✅ ${service.name} started successfully`, 'green');
          resolve(child);
        }
      }
    });

    child.stderr.on('data', (data) => {
      const error = data.toString();
      console.error(`[${service.name}] ERROR: ${error.trim()}`);
    });

    child.on('error', (error) => {
      log(`❌ Failed to start ${service.name}: ${error.message}`, 'red');
      reject(error);
    });

    child.on('exit', (code) => {
      if (code !== 0) {
        log(`❌ ${service.name} exited with code ${code}`, 'red');
      } else {
        log(`🛑 ${service.name} stopped`, 'yellow');
      }
    });

    // Timeout after 30 seconds
    setTimeout(() => {
      if (!started) {
        log(`⏰ ${service.name} startup timeout`, 'yellow');
        resolve(child);
      }
    }, 30000);
  });
};

// Function to check if port is available
const checkPort = (port) => {
  return new Promise((resolve) => {
    const net = require('net');
    const server = net.createServer();
    
    server.listen(port, () => {
      server.once('close', () => {
        resolve(true);
      });
      server.close();
    });
    
    server.on('error', () => {
      resolve(false);
    });
  });
};

// Main function
const main = async () => {
  log('🎯 VWork Microservices Startup', 'bright');
  log('================================', 'bright');

  // Check if ports are available
  log('🔍 Checking port availability...', 'yellow');
  for (const service of services) {
    const available = await checkPort(service.port);
    if (!available) {
      log(`⚠️  Port ${service.port} is already in use (${service.name})`, 'yellow');
    }
  }

  // Start services
  const processes = [];
  
  for (const service of services) {
    try {
      const process = await startService(service);
      processes.push({ service, process });
      
      // Wait a bit between starting services
      await new Promise(resolve => setTimeout(resolve, 2000));
    } catch (error) {
      log(`❌ Failed to start ${service.name}`, 'red');
    }
  }

  log('\n🎉 All services startup completed!', 'green');
  log('================================', 'bright');
  log('📋 Service Status:', 'cyan');
  
  services.forEach(service => {
    log(`   • ${service.name}: http://localhost:${service.port}`, 'blue');
  });

  log('\n🏥 Health Checks:', 'cyan');
  services.forEach(service => {
    log(`   • ${service.name}: http://localhost:${service.port}/health`, 'blue');
  });

  log('\n🌐 API Gateway: http://localhost:8080', 'green');
  log('📖 API Docs: http://localhost:8080/api/v1', 'green');

  // Handle graceful shutdown
  process.on('SIGINT', () => {
    log('\n🛑 Shutting down all services...', 'yellow');
    
    processes.forEach(({ service, process }) => {
      log(`🛑 Stopping ${service.name}...`, 'yellow');
      process.kill('SIGTERM');
    });

    setTimeout(() => {
      log('👋 All services stopped. Goodbye!', 'green');
      process.exit(0);
    }, 3000);
  });

  // Keep the script running
  log('\n💡 Press Ctrl+C to stop all services', 'cyan');
};

// Run the script
if (require.main === module) {
  main().catch(error => {
    log(`❌ Startup failed: ${error.message}`, 'red');
    process.exit(1);
  });
}

module.exports = { startService, services };
