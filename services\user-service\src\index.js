const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3002;

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json());

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    service: 'User Service',
    port: PORT,
    timestamp: new Date().toISOString()
  });
});

// User routes
app.get('/users', (req, res) => {
  console.log('👥 Get users request');
  res.json({
    success: true,
    message: 'Users retrieved successfully',
    data: []
  });
});

app.post('/users', (req, res) => {
  console.log('👤 Create user request:', req.body);
  res.json({
    success: true,
    message: 'User created successfully',
    data: { id: Date.now(), ...req.body }
  });
});

app.listen(PORT, () => {
  console.log(`🚀 User Service running on port ${PORT}`);
  console.log(`🏥 Health check: http://localhost:${PORT}/health`);
});

module.exports = app;
