const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
require('dotenv').config();

// Import shared utilities
const { responseMiddleware, createPagination } = require('../../shared/utils/response');
const { verifyFirebaseToken, optionalAuth, requireOwnership } = require('../../shared/middleware/auth');
const { validateBody, validateQuery, validateParams, schemas } = require('../../shared/middleware/validation');

const app = express();
const PORT = process.env.PORT || 3002;

// Middleware
app.use(helmet());
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3006', 'http://localhost:8080'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'x-auth-token']
}));
app.use(morgan('combined'));
app.use(express.json());
app.use(responseMiddleware);

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    service: 'User Service',
    port: PORT,
    timestamp: new Date().toISOString()
  });
});

// User routes
app.get('/users/:id', optionalAuth, async (req, res) => {
  try {
    const { id } = req.params;

    console.log(`👤 Get user profile: ${id}`);

    // TODO: Get user from database
    const mockUser = {
      uid: id,
      email: '<EMAIL>',
      name: 'John Doe',
      userType: 'freelancer',
      profile: {
        bio: 'Experienced web developer',
        avatar: 'https://example.com/avatar.jpg',
        location: {
          country: 'Vietnam',
          city: 'Ho Chi Minh City',
          timezone: 'Asia/Ho_Chi_Minh'
        },
        website: 'https://johndoe.dev',
        phoneNumber: '+84123456789',
        skills: ['JavaScript', 'React', 'Node.js'],
        hourlyRate: 25,
        availability: 'available',
        isComplete: true
      },
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: new Date().toISOString()
    };

    res.apiSuccess(mockUser, 'User profile retrieved');

  } catch (error) {
    console.error('❌ Get user failed:', error);
    res.apiError('Failed to get user profile', 'GET_USER_ERROR', 500);
  }
});

app.put('/users/:id', verifyFirebaseToken, requireOwnership('id'), validateBody(schemas.userProfile), async (req, res) => {
  try {
    const { id } = req.params;
    const { profile } = req.body;

    console.log(`👤 Update user profile: ${id}`);

    // TODO: Update user in database
    const updatedUser = {
      uid: id,
      profile: {
        ...profile,
        updatedAt: new Date().toISOString()
      },
      updatedAt: new Date().toISOString()
    };

    console.log('✅ Profile updated:', updatedUser);

    res.apiSuccess(updatedUser, 'Profile updated successfully');

  } catch (error) {
    console.error('❌ Update user failed:', error);
    res.apiError('Failed to update profile', 'UPDATE_USER_ERROR', 500);
  }
});

app.get('/freelancers', validateQuery(schemas.pagination), async (req, res) => {
  try {
    const { page = 1, limit = 20, skills, location, hourlyRate, availability } = req.query;

    console.log('👥 Get freelancers:', { page, limit, skills, location, hourlyRate, availability });

    // TODO: Get freelancers from database with filters
    const mockFreelancers = [
      {
        uid: 'freelancer-1',
        name: 'Alice Johnson',
        profile: {
          bio: 'Full-stack developer with 5 years experience',
          avatar: 'https://example.com/alice.jpg',
          location: { country: 'Vietnam', city: 'Hanoi' },
          skills: ['React', 'Node.js', 'MongoDB'],
          hourlyRate: 30,
          availability: 'available'
        },
        rating: 4.8,
        completedProjects: 25
      },
      {
        uid: 'freelancer-2',
        name: 'Bob Smith',
        profile: {
          bio: 'UI/UX Designer and Frontend Developer',
          avatar: 'https://example.com/bob.jpg',
          location: { country: 'Vietnam', city: 'Da Nang' },
          skills: ['Figma', 'React', 'CSS'],
          hourlyRate: 25,
          availability: 'busy'
        },
        rating: 4.6,
        completedProjects: 18
      }
    ];

    const total = mockFreelancers.length;
    const pagination = createPagination(page, limit, total);

    res.apiSuccess(mockFreelancers, 'Freelancers retrieved successfully', pagination);

  } catch (error) {
    console.error('❌ Get freelancers failed:', error);
    res.apiError('Failed to get freelancers', 'GET_FREELANCERS_ERROR', 500);
  }
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('🚨 User Service Error:', error);
  res.apiError('Internal server error', 'INTERNAL_ERROR', 500);
});

app.listen(PORT, () => {
  console.log(`👥 User Service running on port ${PORT}`);
  console.log(`🏥 Health check: http://localhost:${PORT}/health`);
});

module.exports = app;
